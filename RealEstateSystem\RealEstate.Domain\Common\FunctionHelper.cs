﻿using System.Text.RegularExpressions;
using System.ComponentModel;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;

namespace RealEstate.Domain.Common
{
    public static class FunctionHelper
    {
        public static bool IsVietnamesePhoneNumberValid(string number)
        {
            return Regex.IsMatch(number, @"(((\+|)84)|0)(3|5|7|8|9)+([0-9]{8})\b");
        }

        /// <summary>
        /// Validates if the provided notification type is valid according to the NotificationType enum
        /// </summary>
        /// <param name="type">The notification type to validate</param>
        /// <returns>True if the type is valid, false otherwise</returns>
        public static bool IsValidNotificationType(string type)
        {
            // Check if the type can be parsed to the NotificationType enum
            return Enum.TryParse(typeof(EnumValues.NotificationType), type, true, out _);
        }

        /// <summary>
        /// Validates if the provided notification type is valid according to the NotificationType enum
        /// </summary>
        /// <param name="type">The notification type to validate</param>
        /// <returns>True if the type is valid, false otherwise</returns>
        public static bool IsValidNotificationCategory(string category)
        {
            // Check if the type can be parsed to the NotificationType enum
            return Enum.TryParse(typeof(EnumValues.NotificationCategory), category, true, out _);
        }

        /// <summary>
        /// Gets the description value from an enum
        /// </summary>
        /// <param name="enumValue">The enum value</param>
        /// <returns>The description or the enum name if no description exists</returns>
        public static string GetEnumDescription(Enum enumValue)
        {
            FieldInfo? field = enumValue.GetType().GetField(enumValue.ToString());
            if (field != null)
            {
                DescriptionAttribute? attribute = field.GetCustomAttribute<DescriptionAttribute>();
                if (attribute != null)
                {
                    return attribute.Description;
                }
            }
            return enumValue.ToString();
        }

        /// <summary>
        /// Gets the PropertyEngagementEventType enum from string description
        /// </summary>
        /// <param name="description">The description value</param>
        /// <returns>The enum value if found, null otherwise</returns>
        public static EnumValues.PropertyEngagementEventType? GetEngagementEventTypeFromDescription(string description)
        {
            var values = Enum.GetValues<EnumValues.PropertyEngagementEventType>();
            foreach (var value in values)
            {
                if (GetEnumDescription(value).Equals(description, StringComparison.OrdinalIgnoreCase))
                {
                    return value;
                }
            }
            return null;
        }

        /// <summary>
        /// Validates if the provided event type is valid according to the PropertyEngagementEventType enum
        /// </summary>
        /// <param name="eventType">The event type to validate</param>
        /// <returns>True if the event type is valid, false otherwise</returns>
        public static bool IsValidPropertyEngagementEventType(string eventType)
        {
            return GetEngagementEventTypeFromDescription(eventType) != null;
        }

        /// <summary>
        /// Computes the SHA256 hash of a given string.
        /// </summary>
        /// <param name="rawData">The string to hash.</param>
        /// <returns>The SHA256 hash as a hexadecimal string.</returns>
        public static string ComputeSha256Hash(string rawData)
        {
            // Create a SHA256 object
            using (SHA256 sha256Hash = SHA256.Create())
            {
                // ComputeHash - returns byte array
                byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(rawData));

                // Convert byte array to a string
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2"));
                }
                return builder.ToString();
            }
        }
    }
}
