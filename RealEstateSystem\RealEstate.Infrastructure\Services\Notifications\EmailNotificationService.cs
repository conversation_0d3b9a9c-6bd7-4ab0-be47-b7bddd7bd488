﻿using Microsoft.Extensions.Logging;
using RealEstate.Application.DTO.Notification;
using RealEstate.Application.Interfaces;
using Shared.Enums;
using Shared.Results;

namespace RealEstate.Infrastructure.Services.Notifications
{
    public class EmailNotificationService : INotificationService
    {
        private readonly IEmailSender _emailSender;
        private readonly ILogger<EmailNotificationService> _logger;

        public EmailNotificationService(IEmailSender emailSender, ILogger<EmailNotificationService> logger)
        {
            _emailSender = emailSender;
            _logger = logger;
        }
        public async Task<Result> SendAsync(NotificationRequest request)
        {
            // 1. L<PERSON>y thông tin người nhận (email, tên...)
            var subject = request.Title ?? "Thông báo từ hệ thống";
            var recipientEmail = request.RecipientEmail ?? string.Empty;

            // 2. Xác định template dựa vào 'EmailType'
            var templateName = $"{request.EmailType}.html";
            var templatePath = Path.Combine("Templates", templateName);

            if (!File.Exists(templatePath))
            {
                _logger.LogWarning("Template {TemplateName} không tồn tại.", templateName);
                return Result.Failure($"Template {templateName} không tồn tại.", ErrorType.NotFound);
            }

            var emailBody = await File.ReadAllTextAsync(templatePath);

            // 3. Thay thế các biến trong template bằng dữ liệu từ Dictionary 'Data'
            if (request.Data != null)
            {
                foreach (var entry in request.Data)
                {
                    // Thay thế {{Key}} bằng Value
                    emailBody = emailBody.Replace($"{{{entry.Key}}}", entry.Value);
                }
            }

            // 5. Gửi email với nội dung đã được hoàn thiện
            return await _emailSender.SendEmailAsync(recipientEmail, request.Title, emailBody);
        }
    }
}
