﻿using RealEstate.Application.DTO;
using RealEstate.Application.DTO.UserFavorite;
using Shared.Results;

namespace RealEstate.Application.Interfaces
{
    public interface IUserFavoriteService
    {
        Task<Result<IEnumerable<UserFavoriteDto>>> GetUserFavoritesAsync(Guid userId);
        Task<Result<PagedFavoriteResultDto>> GetUserFavoritesWithDetailsAsync(Guid userId, FavoriteFilterDto filter);
        Task<Result> AddToFavoritesAsync(Guid userId, Guid propertyId);
        Task<Result> RemoveFromFavoritesAsync(Guid userId, Guid propertyId);
        Task<Result<List<FavoriteStatusDto>>> CheckFavoriteStatusAsync(Guid userId, List<Guid> propertyIds);
        Task<Result<int>> GetFavoritesCountAsync(Guid userId);
    }
}
