using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Analytics;
using RealEstate.Domain.Entities;
using Shared.Results;

namespace RealEstate.Application.Interfaces
{
    public interface IPropertyAnalyticsService
    {
        // Core logging methods
        Task<Result> LogPropertyViewAsync(LogPropertyViewDto dto);
        Task<Result> LogPropertyEngagementEventAsync(LogPropertyEngagementEventDto dto);
        
        // Get analytics
        Task<Result<PropertyAnalyticsDto>> GetPropertyAnalyticsAsync(Guid propertyId, DateTime? startDate = null, DateTime? endDate = null);
        Task<Result<PagedResultDto<PropertyAnalyticsDto>>> GetUserPropertiesAnalyticsAsync(Guid userId, PropertyAnalyticsFilterDto filter);
        
        // Export analytics
        Task<Result<byte[]>> ExportPropertyAnalyticsToExcelAsync(Guid propertyId, DateTime? startDate = null, DateTime? endDate = null);
        Task<Result<byte[]>> ExportUserPropertiesAnalyticsToExcelAsync(Guid userId, PropertyAnalyticsFilterDto filter);
        
        // Get property engagement summary
        Task<Result<object>> GetPropertyEngagementSummaryAsync(Guid propertyId);

        // Update engagement summary (can be called by a background job)
        Task<Result> UpdatePropertyEngagementSummaryAsync(Guid propertyId);
        Task<Result<PropertyEngagementSummary>> UpdatePropertyEngagementSummaryAsync(Property property);
        Task<Result> UpdateAllPropertiesEngagementSummaryAsync();
        Task<Result<IEnumerable<PropertyStatusLogDto>>> GetPropertyHistoryStatus(Guid propertyId);
    }
}
