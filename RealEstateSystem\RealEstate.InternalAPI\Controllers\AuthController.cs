﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Application.Services;
using RealEstate.Domain.Common;
using Shared.Constants;
using Shared.Responses;
using Shared.Results;
using System.Security.Claims;

namespace RealEstate.InternalAPI.Controllers
{
    /// <summary>
    /// Controller for authentication and user-related operations.
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : BaseController
    {
        private readonly IAuthService _authService;
        private readonly IUserService _userService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(IAuthService authService, ILogger<AuthController> logger, IUserService userService)
        {
            _authService = authService;
            _logger = logger;
            _userService = userService;
        }

        /// <summary>
        /// Authenticates a user and provides a JWT token along with a refresh token.
        /// </summary>
        /// <param name="loginDto">The login credentials.</param>
        /// <returns>A UserDto containing user details and a JWT token.</returns>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login(LoginDto loginDto)
        {
            _logger.LogInformation("Attempting to log in user with email: {Email}", loginDto.Email);
            var userResult = await _authService.LoginAsync(loginDto);

            if (userResult.IsSuccess)
            {
                var user = userResult.Value;

                Response.Cookies.Append("_yh.utk", user.Token, new CookieOptions
                {
                    HttpOnly = true,
                    Secure = true, // Enable on HTTPS
                    SameSite = SameSiteMode.Strict,
                    Expires = DateTime.UtcNow.AddHours(7)
                });

                userResult.Value.Token = null;
            }
            return HandleResult(userResult);
        }

        [HttpGet("me")]
        public async Task<IActionResult> GetUserProfile()
        {
            var userIdString = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (userIdString == null)
            {
                var unauthorizedResponse = ApiResponse<object>.Failure("Unauthorized: User ID not found in token.");
                return Unauthorized(unauthorizedResponse);
            }

            var parsedUserId = Guid.Parse(userIdString);

            // Chỉ cần gọi một hàm duy nhất từ service
            var result = await _userService.GetUserByIdAsync(parsedUserId, isIncludeWalletInfo: false);

            return HandleResult(result);
        }

        /// <summary>
        /// Authenticates a user and provides a JWT token along with a refresh token.
        /// </summary>
        /// <param name="loginDto">The login credentials.</param>
        /// <returns>A UserDto containing user details and a JWT token.</returns>
        [HttpPost("login-mobile")]
        [AllowAnonymous]
        public async Task<IActionResult> LoginMobile(LoginDto loginDto)
        {
            _logger.LogInformation("Attempting to log in user with email: {Email}", loginDto.Email);
            try
            {
                var userResult = await _authService.LoginAsync(loginDto);
                if (userResult.IsSuccess)
                {
                    var user = userResult.Value;
                    if (user == null || user.Token == null || user.UserType != EnumValues.UserType.Admin)
                    {
                        _logger.LogWarning("Unauthorized: {Email}", loginDto.Email);
                        return Unauthorized(new { Message = "Unauthorized" });
                    }
                }

                return HandleResult(userResult);
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when logging in");
                return Unauthorized(new { Message = "An error occurred during login. Please try again later." });
            }
        }

        [HttpPost("logout")]
        public IActionResult Logout()
        {
            Response.Cookies.Delete("_yh.utk");
            return HandleResult(Result.Success());
        }

    }
}
