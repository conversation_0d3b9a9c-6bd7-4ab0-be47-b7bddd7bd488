﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RealEstate.Domain.Entities
{
    public class MemberRanking : BaseEntity
    {
        [Key]
        public string RankName { get; set; }

        [Column(TypeName = "numeric(20,2)")]
        public decimal? MinSpent { get; set; }

        [Column(TypeName = "numeric(20,2)")]
        public decimal? MaxSpent { get; set; }

        // Navigation property for the related HighlightFee
        public virtual HighlightFee HighlightFee { get; set; }
    }
}
