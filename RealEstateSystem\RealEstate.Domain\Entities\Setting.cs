﻿using System.ComponentModel.DataAnnotations;

namespace RealEstate.Domain.Entities
{
    public class Setting : BaseEntity
    {
        [MaxLength(100)]
        public string Key { get; set; } = null!;

        public string Value { get; set; } = null!;

        [MaxLength(20)]
        public string? Type { get; set; } = "string";

        public string? Description { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime UpdatedAt { get; set; }
    }
}
