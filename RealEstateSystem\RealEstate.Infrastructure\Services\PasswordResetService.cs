﻿using RealEstate.Application.Interfaces;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using Shared.Enums;
using Shared.Results;
using System.Security.Cryptography;

namespace RealEstate.Application.Services
{
    public class PasswordResetService : IPasswordResetService
    {
        private readonly IUnitOfWork _unitOfWork;

        public PasswordResetService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<Result<string>> GenerateAndStoreResetToken(Guid userId)
        {
            // Tạo một chuỗi token ngẫu nhiên và an toàn
            // Sử dụng byte array để tạo token và sau đó chuyển sang Base64Url
            // để đảm bảo an toàn khi truyền qua URL.
            byte[] tokenBytes = new byte[32]; // 32 bytes = 256 bits, đủ an toàn
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(tokenBytes);
            }
            string rawToken = Convert.ToBase64String(tokenBytes)
                                   .Replace('+', '-') // Thay thế ký tự không an toàn cho URL
                                   .Replace('/', '_')
                                   .Replace("=", ""); // Fix: Replace empty character literal with a string

            // Đặt thời gian hết hạn (ví dụ: 60 phút từ bây giờ)
            var expirationTime = DateTime.UtcNow.AddMinutes(60);

            // Tạo đối tượng PasswordResetToken
            var resetToken = new PasswordResetToken
            {
                UserId = userId,
                Token = rawToken,
                ExpirationTime = expirationTime,
                IsUsed = false,
                CreatedAt = DateTime.UtcNow
            };

            // Lưu token vào cơ sở dữ liệu
            await _unitOfWork.PasswordResetTokens.AddAsync(resetToken);
            await _unitOfWork.SaveChangesAsync();

            return Result<string>.Success(rawToken);
        }

        public async Task<Result<(bool isValid, Guid userId)>> ValidateResetToken(string token)
        {
            if (string.IsNullOrEmpty(token))
            {
                return Result<(bool isValid, Guid userId)>.Failure("Token not found", ErrorType.NotFound);
            }

            // Tìm token trong DB
            var resetToken = await _unitOfWork.PasswordResetTokens.FindOneAsync(t => t.Token == token);

            if (resetToken == null)
            {
                return Result<(bool isValid, Guid userId)>.Failure("Token not found", ErrorType.NotFound);
            }


            // Kiểm tra thời gian hết hạn
            if (resetToken.ExpirationTime < DateTime.UtcNow)
            {
                // Đánh dấu là đã sử dụng (hoặc xóa) nếu hết hạn để tránh tái sử dụng
                resetToken.IsUsed = true;
                await _unitOfWork.SaveChangesAsync(); // Fix: Use _unitOfWork here instead of _context
                return Result<(bool isValid, Guid userId)>.Failure("Token expired", ErrorType.Validation); // Fix: Return type consistency
            }

            // Kiểm tra đã sử dụng chưa
            if (resetToken.IsUsed)
            {
                return Result<(bool isValid, Guid userId)>.Failure("Token has been used", ErrorType.Validation); // Fix: Return type consistency
            }

            // Nếu hợp lệ, trả về true và UserId
            return Result<(bool isValid, Guid userId)>.Success((true, resetToken.UserId));
        }

        public async Task<Result> MarkTokenAsUsed(string token)
        {
            var resetToken = await _unitOfWork.PasswordResetTokens.FindOneAsync(t => t.Token == token);
            if (resetToken == null)
            {
                return Result.Failure("Token not found", ErrorType.NotFound);
            }

            resetToken.IsUsed = true;
            await _unitOfWork.SaveChangesAsync();

            return Result.Success();
        }

        public async Task<Result<PasswordResetToken>> GetTokenByValue(string token)
        {
            var resetToken = await _unitOfWork.PasswordResetTokens.FindOneAsync(t => t.Token == token);
            if (resetToken == null)
            {
                return Result<PasswordResetToken>.Failure("Token not found", ErrorType.NotFound);
            }

            return Result<PasswordResetToken>.Success(resetToken);
        }
    }
}
