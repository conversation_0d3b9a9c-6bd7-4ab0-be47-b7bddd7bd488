﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RealEstate.Application.DTO
{
    public class SettingDto
    {
        public string Key { get; set; } = null!;
        public string Value { get; set; } = null!;
    }

    public class SettingCreateRequest
    {
        public string Key { get; set; } = null!;
        public string Value { get; set; } = null!;
        public string? Description { get; set; }
    }

    public class SettingUpdateItem
    {
        public string Key { get; set; } = null!;
        public string Value { get; set; } = null!;
    }

    public class SettingBulkUpdateRequest
    {
        public List<SettingUpdateItem> Items { get; set; } = new();
    }
}
