﻿using RealEstate.Application.DTO;
using RealEstate.Domain.CustomModel;
using Shared.Results;

namespace RealEstate.Application.Interfaces
{
    public interface IContactRequestService
    {
        Task<Result<ContactRequestDto>> GetByIdAsync(Guid id);
        Task<Result<IEnumerable<ContactRequestDto>>> GetAllAsync();
        Task<Result<PagedResult<ContactRequestDto>>> GetPagedAsync(PagingRequest paging, string? phone);
        Task<Result<IEnumerable<ContactRequestDto>>> GetByPropertyIdAsync(Guid propertyId);
        Task<Result<ContactRequestDto>> CreateAsync(CreateContactRequestDto requestDto);
        Task<Result> UpdateAsync(Guid id, UpdateContactRequestDto requestDto);
        Task<Result> DeleteAsync(Guid id);
    }
}
