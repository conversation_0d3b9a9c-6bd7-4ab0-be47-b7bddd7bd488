﻿using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;

namespace RealEstate.InternalAPI.Controllers
{
    
    /// <summary>
    /// Controller for internal user management operations suchs as roles, dashboard, reactivation, and invoice info.
    /// </summary>
    [Route("api/[controller]")]
    public class UserController : BaseController
    {
        private readonly IUserService _userService;
        private readonly IUserDashboardService _dashboardService;

        public UserController(IUserService userService, IUserDashboardService dashboardService)
        {
            _userService = userService;
            _dashboardService = dashboardService;
        }

        /// <summary>
        /// Retrieves user information by user ID (internal use).
        /// </summary>
        /// <param name="id">The user ID.</param>
        /// <returns>User information.</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetUser(Guid id)
        {
            var user = await _userService.GetUserByIdAsync(id);
            return HandleResult(user);
        }

        /// <summary>
        /// Retrieves the dashboard for a user by user ID (internal use).
        /// </summary>
        /// <param name="userId">The user ID.</param>
        /// <returns>User dashboard data.</returns>
        [HttpGet("dashboard/{userId}")]
        public async Task<IActionResult> GetUserDashboardById(Guid userId)
        {
            var dashboard = await _dashboardService.GetUserDashboardAsync(userId);
            return HandleResult(dashboard);
        }

        /// <summary>
        /// Reactivates a user account (admin only).
        /// </summary>
        /// <param name="userId">The user ID.</param>
        /// <returns>Status of the reactivation.</returns>
        [HttpPost("reactivate/{userId}")]
        public async Task<IActionResult> ReactivateAccount(Guid userId)
        {
            var result = await _userService.ReactivateUserAsync(userId);
            return HandleResult(result);
        }        

        /// <summary>
        /// Retrieves invoice information for a user (admin only).
        /// </summary>
        /// <param name="id">The user ID.</param>
        /// <returns>User invoice information.</returns>
        [HttpGet("{id}/invoice-info")]
        public async Task<IActionResult> GetUserInvoiceInfo(Guid id)
        {
            var invoiceInfo = await _userService.GetUserInvoiceInfoAsync(id);
            return HandleResult(invoiceInfo);
        }

        /// <summary>
        /// Get all customer users with pagination and filtering
        /// </summary>
        /// <param name="filter">Filter criteria including pagination, email, name, phone filters</param>
        /// <returns>Paginated list of customer users</returns>
        [HttpGet("customers")]
        public async Task<IActionResult> GetCustomers([FromQuery] UserFilterDto filter)
        {
            var users = await _userService.GetNonAdminUsersAsync(filter);
            return HandleResult(users);
        }

        /// <summary>
        /// Get all employee users with pagination and filtering
        /// </summary>
        /// <param name="filter">Filter criteria including pagination, email, name, phone filters</param>
        /// <returns>Paginated list of employee users</returns>
        [HttpGet("employees")]
        public async Task<IActionResult> GetEmployees([FromQuery] UserFilterDto filter)
        {
            var users = await _userService.GetAdminUsersAsync(filter);
            return HandleResult(users);
        }

        /// <summary>
        /// Activate or deactivate a user account
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="updateStatusDto">The status update data</param>
        /// <returns>Status of the operation</returns>
        [HttpPut("{userId}/status")]
        public async Task<IActionResult> UpdateUserStatus(Guid userId, [FromBody] UpdateUserStatusDto updateStatusDto)
        {
            var result = await _userService.UpdateUserStatusAsync(userId, updateStatusDto.IsActive);
            return HandleResult(result);
        }

        /// <summary>
        /// Create a new employee user with roles
        /// </summary>
        /// <param name="createAdminUserDto">The employee user creation data</param>
        /// <returns>The created employee user</returns>
        [HttpPost("employees")]
        public async Task<IActionResult> CreateEmployee([FromBody] CreateAdminUserDto createAdminUserDto)
        {
            var user = await _userService.CreateAdminUserAsync(createAdminUserDto);
            return HandleResult(user);
        }

        /// <summary>
        /// Update user roles
        /// </summary>
        /// <param name="updateUserRoleDto">The user role update data</param>
        /// <returns>Status of the operation</returns>
        [HttpPut("roles")]
        public async Task<IActionResult> UpdateUserRoles([FromBody] UpdateUserRoleDto updateUserRoleDto)
        {
            var result = await _userService.UpdateUserRolesAsync(updateUserRoleDto);
            return HandleResult(result);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteUser(Guid id)
        {
            var result = await _userService.DeleteUserAsync(id);
            return HandleResult(result);
        }
    }
}
