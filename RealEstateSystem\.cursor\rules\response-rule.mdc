---
description:
globs:
alwaysApply: false
---
# Ở tầng Services
- Service sẽ luôn trả về 1 đối tượng Result `Shared/Results/Results.cs`
- Đối với các service method có trả về data thì sử dụng Result<T>
  - Tr<PERSON><PERSON><PERSON> hợp lỗi trả về Result<T>.Failure
  - Trường hợp thành công trả về Result<T>.Success(T)
- Đối với các service method không trả về data thì sử dung Result
  - Trường hợp lỗi trả về Result.Failure
  - Trườ<PERSON> hợp thành công trả về Result.Success()
- kết hợp với `log-rule.mdc` để ghi log ở những chỗ quan trọng trong service. Note: nhớ rõ chỉ ghi những logic quan trọng, không ghi log tràn lan một cách không kiểm soát.

##  Service trả về lỗi với đúng loại của nó
- T<PERSON><PERSON><PERSON><PERSON> hợp có error trả về thì cần thêm error type trong `Shared/Enums/ErrorType.cs`

- Code ví dụ:
  ```
  public async Task<Result<Product>> UpdateProductAsync(int id, ProductUpdateDto dto)
  {
      var product = await _context.Products.FindAsync(id);
      if (product == null)
      {
          // Lỗi không tìm thấy
          return Result<Product>.Failure($"Không tìm thấy sản phẩm với ID = {id}.", ErrorType.NotFound);
      }

      if (string.IsNullOrWhiteSpace(dto.Name))
      {
          // Lỗi dữ liệu không hợp lệ
          return Result<Product>.Failure("Tên sản phẩm không được để trống.", ErrorType.Validation);
      }
      // ...
  }
  ```

# Ở tầng controller
- các controller phải luôn trả về lớp ApiResponse thông qua hàm HandleResult(result). 
- tầng controller không chưa logic nghiệp vụ, tất cả sẽ được xử lý ở services, controller chỉ điều hướng và nhận kết quả.
  Code ví dụ:

  ```
  [HttpGet("{id}")]
  public async Task<IActionResult> GetPropertyById(Guid id)
  {
      var result = await _propertyService.GetPropertyByIdAsync(id);
      
      // Dùng hàm helper từ BaseController để xử lý
      return HandleResult(result);
  }
  
  [HttpPost]
  public async Task<IActionResult> CreateProperty([FromBody] CreatePropertyDto dto)
  {
      var result = await _propertyService.CreatePropertyAsync(dto);
      return HandleResult(result);
  }
  ```
- kết hợp với `log-rule.mdc` để ghi log ở controller.

