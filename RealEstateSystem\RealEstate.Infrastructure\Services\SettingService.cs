﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using Shared.Constants;
using Shared.Enums;
using Shared.Results;

namespace RealEstate.Infrastructure.Services
{
    public class SettingService : ISettingService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public SettingService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<Result> CreateAsync(SettingCreateRequest request)
        {
            // Check if setting with the same key already exists
            var existingSetting = await _unitOfWork.Settings.GetQueryable()
                .FirstOrDefaultAsync(s => s.Key == request.Key);

            if (existingSetting != null)
            {
                return Result.Failure(SettingMessage.SettingKeyAlreadyExists, ErrorType.Validation);
            }

            var setting = _mapper.Map<Setting>(request);
            setting.CreatedAt = DateTime.UtcNow;
            setting.UpdatedAt = DateTime.UtcNow;

            await _unitOfWork.Settings.AddAsync(setting);
            await _unitOfWork.SaveChangesAsync();

            return Result.Success();
        }

        public async Task<Result<List<SettingDto>>> GetAllAsync()
        {
            var settings = await _unitOfWork.Settings.GetAllAsync();
            var settingDtos = _mapper.Map<List<SettingDto>>(settings);
            return Result<List<SettingDto>>.Success(settingDtos);
        }

        public async Task<Result<SettingDto>> GetByKeyAsync(string key)
        {
            var setting = await _unitOfWork.Settings.GetQueryable()
                .FirstOrDefaultAsync(s => s.Key == key);

            if (setting == null)
            {
                return Result<SettingDto>.Failure(SettingMessage.SettingNotFound, ErrorType.NotFound);
            }

            var settingDto = _mapper.Map<SettingDto>(setting);
            return Result<SettingDto>.Success(settingDto);
        }

        public async Task<Result> UpdateAsync(string key, string value)
        {
            var setting = await _unitOfWork.Settings.GetQueryable()
                .FirstOrDefaultAsync(s => s.Key == key);

            if (setting == null)
            {
                return Result.Failure(SettingMessage.SettingNotFound, ErrorType.NotFound);
            }

            setting.Value = value;
            setting.UpdatedAt = DateTime.UtcNow;

            _unitOfWork.Settings.Update(setting);
            await _unitOfWork.SaveChangesAsync();

            return Result.Success();
        }

        public async Task<Result> BulkUpdateAsync(SettingBulkUpdateRequest request)
        {
            if (request.Items == null || !request.Items.Any())
            {
                return Result.Failure(SettingMessage.InvalidSettingData, ErrorType.Validation);
            }

            var keys = request.Items.Select(item => item.Key).ToList();
            var settings = await _unitOfWork.Settings.GetQueryable()
                .Where(s => keys.Contains(s.Key))
                .ToListAsync();

            var updatedCount = 0;
            foreach (var item in request.Items)
            {
                var setting = settings.FirstOrDefault(s => s.Key == item.Key);
                if (setting != null)
                {
                    setting.Value = item.Value;
                    setting.UpdatedAt = DateTime.UtcNow;
                    _unitOfWork.Settings.Update(setting);
                    updatedCount++;
                }
            }

            if (updatedCount == 0)
            {
                return Result.Failure(SettingMessage.SettingNotFound, ErrorType.NotFound);
            }

            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }
    }
}
