using RealEstate.Application.DTO;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RealEstate.Application.Interfaces
{
    public interface IYezDataService
    {
        Task<IEnumerable<HighlightFeeDto>> GetAllHighlightFeesAsync();
        Task<HighlightFeeDto> GetHighlightFeeByRankAsync(string rankName);
        Task<IEnumerable<MemberRankListDto>> GetAllMemberRankingsAsync();
        Task<MemberRankListDto> GetMemberRankingByNameAsync(string rankName);
    }
}
