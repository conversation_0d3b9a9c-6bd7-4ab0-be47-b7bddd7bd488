﻿using RealEstate.Application.DTO;
using Shared.Results;

namespace RealEstate.Application.Interfaces
{
    public interface IUserService
    {
        Task<Result<ProfileDto>> GetUserProfileAsync(Guid userId);
        Task<Result<bool>> IsUserExistsAsync(Guid userId);
        Task<Result<bool>> IsUserAdminExistsAsync(Guid userId);
        Task<Result<UserDto>> GetUserByIdAsync(Guid id, bool isIncludeWalletInfo = true);
        Task<Result<UserDto>> GetUserByEmailAsync(string email);
        Task<Result<IEnumerable<UserDto>>> GetAllUsersAsync();
        Task<Result<UserDto>> CreateUserAsync(CreateUserDto userDto);
        Task<Result> UpdateUserAsync(Guid id, CreateUserDto userDto);
        Task<Result> DeleteUserAsync(Guid id);
        Task<Result> AddUserRoleAsync(AddUserRoleDto addUserRoleDto);
        Task<Result> UpdateUserAvatarAsync(Guid userId, string avatarImage);
        Task<Result<string>> GetUserAvatarImageAsync(Guid userId);
        Task<Result> DeactivateUserAsync(Guid userId, DeactivateUserDto deactivateUserDto);
        Task<Result> ReactivateUserAsync(Guid userId);
        Task<Result> PermanentDeleteUserAsync(Guid userId, DeactivateUserDto deactivateUserDto);
        Task<Result> UpdateUserTaxInfoAsync(Guid userId, UserTaxInfoDto taxInfoDto);
        Task<Result<UserTaxInfoDto>> GetUserInvoiceInfoAsync(Guid userId);

        // Permission-related methods
        Task<Result<IEnumerable<PermissionDto>>> GetUserPermissionsAsync(Guid userId);
        Task<Result<bool>> UserHasPermissionAsync(Guid userId, string permissionCode);
        Task<Result<IEnumerable<AdminRoleDto>>> GetUserRolesAsync(Guid userId);

        // User management methods for admin
        Task<Result<PagedResultDto<UserDto>>> GetNonAdminUsersAsync(UserFilterDto filter);
        Task<Result<PagedResultDto<UserDto>>> GetAdminUsersAsync(UserFilterDto filter);
        Task<Result> UpdateUserStatusAsync(Guid userId, bool isActive);
        Task<Result<UserDto>> CreateAdminUserAsync(CreateAdminUserDto createAdminUserDto);
        Task<Result> UpdateUserRolesAsync(UpdateUserRoleDto updateUserRoleDto);
        Task<Result> UpdateUserMainPhoneAsync(UpdateUserPhoneDto updateUserPhoneDto);

        Task<Result<bool>> UpdateUserPassword(Guid userId, string password, string token);
    }
}
