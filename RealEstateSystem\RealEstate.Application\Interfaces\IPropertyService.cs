﻿using RealEstate.Application.DTO;
using Shared.Results;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.Application.Interfaces
{
    public interface IPropertyService
    {
        Task<Result<PropertyDto>> GetPropertyByIdAsync(Guid id, bool asNoTracking = true);
        Task<Result<IEnumerable<PropertyDto>>> GetPropertyByUserAsync(Guid userId);
        Task<Result<PagedResultDto<PropertyDto>>> GetPropertyByUserWithStatusAsync(Guid userId, List<PropertyStatus> statuses, int page = 1, int pageSize = 50);
        Task<Result<PropertyCountStatsDto>> GetPropertyCountStatsByUserAsync(Guid userId);
        Task<Result<PropertyDto>> CreatePropertyAsync(CreatePropertyDto propertyDto, Guid userId);
        Task<Result<PropertyDto>> UpdatePropertyAsync(Guid id, CreatePropertyDto propertyDto, Guid userId);
        Task<Result> DeletePropertyAsync(Guid id, Guid userId);
        Task<Result> DeletePropertiesAsync(List<Guid> ids, Guid userId);
        Task<Result> UpdateStatusAsync(Guid id, Guid userId, UpdateStatusDto updateStatusDto);
        Task<Result> UpdateStatusBulkAsync(List<Guid> ids, Guid userId, UpdateStatusDto updateStatusDto);
        Task<Result<PropertyDto>> UpdateHighlightWithPaymentAsync(Guid propertyId, Guid userId, bool isHighlighted);
        Task<Result<BulkHighlightResultDto>> UpdateHighlightBulkWithPaymentAsync(List<Guid> propertyIds, Guid userId, bool isHighlighted);
        Task<Result<int>> VerifyPropertyRemainingTimes(Guid propertyId);
        Task<Result<PagedResultDto<PropertyDto>>> SearchPropertiesAsync(PropertyFilterCriteriaDto filterCriteria);
        Task<Result<int>> CountPropertiesAsync(PropertyFilterCriteriaDto filterCriteria);
        Task<Result<PropertyCountStatsDto>> GetPropertyCountByStatusAsync();
        Task<Result> UpdatePropertyRenewalAsync(UpdatePropertyRenewalDto updateDto, Guid userId);
        Task<Result<PropertyDto>> RenewPropertyAsync(PropertyRenewalDto request, Guid value);
    }
}
