﻿using RealEstate.Domain.Common;
using Shared.Enums;

namespace RealEstate.Application.DTO.Notification
{
    public class NotificationRequest
    {
        public NotificationChannel TargetChannels { get; set; } = NotificationChannel.All; // Mặc định gửi tất cả
        public Guid? RecipientId { get; set; } // Null nếu là thông báo hệ thống hoặc userId nếu là cho 1 user
        public Guid? RelatedEntityId { get; set; } // Null nếu không phải là thông báo có liên hệ hoặc nạp tiền vào ví (transaction)
        public Guid? RelatedPropertyId { get; set; } // Null nếu là thông báo hệ thống và không liên quan gì bài đăng
        public EnumValues.NotificationType InAppNotificationType { get; set; }
        public string RecipientEmail { get; set; } // Email của người nhận, nếu có, dùng để gửi email
        public string EmailType { get; set; } 
        public string Title { get; set; }
        public string Message { get; set; }
        public Dictionary<string, string> Data { get; set; } // D<PERSON> liệu bổ sung, vd: {"listingId": "123"}
    }
}
