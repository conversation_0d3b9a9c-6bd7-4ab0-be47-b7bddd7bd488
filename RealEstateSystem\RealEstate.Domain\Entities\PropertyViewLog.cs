using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RealEstate.Domain.Entities
{
    public class PropertyEngagementView : BaseEntity
    {
        [Required]
        public Guid PropertyId { get; set; }
        
        public Guid? ViewerId { get; set; }
        
        [StringLength(50)]
        public string? ViewerIP { get; set; }
        
        [Required]
        public DateTime ViewedAt { get; set; } = DateTime.UtcNow;
        
        public string? UserAgent { get; set; }
        
        public string? ReferrerUrl { get; set; }
        
        // New columns from migration
        [StringLength(255)]
        public string? SessionId { get; set; }

        [StringLength(255)]
        public string? DeviceId { get; set; }

        [StringLength(20)]
        public string? DeviceType { get; set; }
        
        [StringLength(50)]
        public string? Platform { get; set; }
        
        [StringLength(50)]
        public string? Browser { get; set; }
        
        [StringLength(100)]
        public string? City { get; set; }
        
        [StringLength(100)]
        public string? Region { get; set; }
        
        [StringLength(100)]
        public string? Country { get; set; }
        
        // Navigation properties
        [ForeignKey("PropertyId")]
        public Property? Property { get; set; }
        
        [ForeignKey("ViewerId")]
        public AppUser? Viewer { get; set; }
    }
}
