﻿using RealEstate.Application.DTO.Notification;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Common;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using Shared.Results;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.Infrastructure.Services.Notifications
{
    public class InAppNotificationService : INotificationService
    {
        private readonly IUnitOfWork _unitOfWork;

        public InAppNotificationService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<Result> SendAsync(NotificationRequest request)
        {
            var notificationType = request.InAppNotificationType;
            var category = NotificationMapper.TypeToCategoryMap[notificationType];

            // Create notification for property owner
            var notification = new Notification
            {
                UserId = request.RecipientId,
                Type = notificationType,
                Category = category,
                Title = request.Title,
                Message = request.Message,
                RelatedEntityId = request.RelatedEntityId,
                RelatedPropertyId = request.RelatedPropertyId,
                ActionUrl = request.Data.ContainsKey("actionUrl") ? request.Data["actionUrl"] : null,
            };

            await _unitOfWork.Notifications.AddAsync(notification);
            await _unitOfWork.SaveChangesAsync();

            return Result.Success();
        }
    }
}
