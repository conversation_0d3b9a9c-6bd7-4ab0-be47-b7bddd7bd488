﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.CustomModel;
using RealEstate.Domain.Common;
using RealEstate.Application.DTO.Notification;

namespace RealEstate.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ContactRequestController : BaseController
    {
        private readonly IContactRequestService _contactRequestService;
        private readonly ILogger<ContactRequestController> _logger;

        public ContactRequestController(
            IContactRequestService contactRequestService,
            ILogger<ContactRequestController> logger)
        {
            _contactRequestService = contactRequestService;
            _logger = logger;
        }

        [Authorize(Policy = "UserExists")]
        [HttpGet("{id}")]
        public async Task<IActionResult> GetContactRequestById(Guid id)
        {
            var result = await _contactRequestService.GetByIdAsync(id);
            return HandleResult(result);
        }

        [HttpGet]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> GetAllContactRequests()
        {
            var result = await _contactRequestService.GetAllAsync();
            return HandleResult(result);
        }

        [HttpGet("requests")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> GetContactRequests([FromQuery] PagingRequest request, [FromQuery] string? phone)
        {
            var result = await _contactRequestService.GetPagedAsync(request, phone ?? string.Empty);
            return HandleResult(result);
        }

        [HttpPost]
        [AllowAnonymous]
        public async Task<IActionResult> CreateContactRequest([FromBody] CreateContactRequestDto requestDto)
        {
            var result = await _contactRequestService.CreateAsync(requestDto);
            return HandleResult(result);
        }

        [HttpPut("{id}")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> UpdateContactRequest(Guid id, [FromBody] UpdateContactRequestDto requestDto)
        {
            var result = await _contactRequestService.UpdateAsync(id, requestDto);
            return HandleResult(result);
        }

        [HttpGet("property/{propertyId}")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> GetContactRequestsByPropertyId(Guid propertyId)
        {
            var result = await _contactRequestService.GetByPropertyIdAsync(propertyId);
            return HandleResult(result);
        }

        [HttpDelete("{id}")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> DeleteContactRequest(Guid id)
        {
            var result = await _contactRequestService.DeleteAsync(id);
            return HandleResult(result);
        }

    }
}
