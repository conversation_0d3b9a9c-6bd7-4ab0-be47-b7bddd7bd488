# Technical Design Document: Property Management Features

## 1. Overview

The Property Management feature is a core component of the YEZ Home Real Estate Management System that enables property owners to list, manage, and sell/rent their properties. The system provides comprehensive functionality for property creation, updating, searching, and media management, along with a status tracking workflow. A key feature is the geolocation-based nearby property search that allows users to find properties within a specific radius of their current location or a point of interest.

## 2. Requirements

### 2.1 Functional Requirements

* Users can create property listings with detailed information including property type, location, price, and features
* Users can upload and manage multiple media files (images) for each property
* Property listings have a defined lifecycle with different statuses (Draft, Pending, Approved, Rejected, Sold)
* Property owners can update their property listings with a limited number of edits after review
* Users can search for properties using a comprehensive set of filters including location, price range, area, and amenities
* The system supports proximity-based searches using geolocation
* Property listings have an expiration date and can be set to auto-renew
* Users can view the status history of their property listings
* Admin users can approve, reject, or modify property listings
* Properties can be highlighted (featured) for better visibility

### 2.2 Non-Functional Requirements

* The system should handle efficient storage and retrieval of property images in multiple resolutions
* Property searches must be optimized for performance with pagination support
* Images should be automatically processed to create different sizes with watermarks
* The system should maintain a complete audit trail of property status changes
* Property data should be securely stored and accessible only to authorized users
* The system should handle location-based proximity searches efficiently

## 3. Technical Design

### 3.1. Data Model Changes

The Property entity sits at the center of the real estate management system, with numerous relationships to other entities:

```mermaid
erDiagram
    Property ||--o{ PropertyMedia : has
    Property ||--o{ PropertyReview : receives
    Property ||--o{ PropertyStatusLog : tracks
    Property ||--o{ UserFavorite : saved_in
    Property }|--|| AppUser : owned_by
    Property }|--o| City : located_in
    Property }|--o| District : located_in
    Property }|--o| Ward : located_in
    Property }|--o| Street : located_in
    Property ||--o{ ContactRequest : generates
    PropertyStatusLog }|--|| AppUser : changed_by
    
    Property {
        Guid Id PK
        Guid OwnerID FK
        string PropertyType
        string PostType
        string Name
        int CityId FK
        int DistrictId FK
        int StreetId FK
        int WardId FK
        string Address
        decimal Area
        decimal Price
        decimal Latitude
        decimal Longitude
        string VideoUrl
        int Floors
        int Rooms
        int Toilets
        string Direction
        string BalconyDirection
        string Legality
        string Interior
        int Width
        int RoadWidth
        string Description
        string Overview
        string PlaceData
        string Policies
        string Neighborhood
        string Status
        decimal PostPrice
        bool IsHighlighted
        bool IsAutoRenew
        DateTime ExpiresAt
        int UpdateRemainingTimes
    }
    
    PropertyMedia {
        Guid Id PK
        Guid PropertyID FK
        string MediaType
        string MediaURL
        string FilePath
        string ThumbnailPath
        string SmallPath
        string MediumPath
        string LargePath
        DateTime UploadedAt
        string Caption
        bool IsAvatar
    }
    
    PropertyStatusLog {
        Guid Id PK
        Guid PropertyID FK
        string Status
        Guid ChangedBy FK
        DateTime ChangedAt
        string Comment
    }
```

### 3.2. API Changes

The system provides RESTful API endpoints for property management:

#### GET /api/Property/{propertyId}
Retrieves details of a specific property by ID.

**Response:**
```json
{
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "name": "Luxury Villa with Pool",
  "propertyType": "Villa",
  "postType": "Sale",
  "cityId": 1,
  "districtId": 2,
  "streetId": 3,
  "wardId": 4,
  "address": "123 Luxury Lane",
  "area": 250.5,
  "price": 5000000000,
  "latitude": 10.762622,
  "longitude": 106.660172,
  "videoUrl": "https://example.com/video.mp4",
  "floors": 2,
  "rooms": 4,
  "toilets": 3,
  "direction": "South",
  "balconyDirection": "East",
  "legality": "Sổ đỏ",
  "interior": "Đầy đủ",
  "width": 15,
  "roadWidth": 10,
  "description": "Beautiful luxury villa with swimming pool and garden",
  "overview": "Modern villa in quiet neighborhood",
  "placeData": "Near shopping mall and schools",
  "policies": "No pets allowed",
  "neighborhood": "Friendly community with parks nearby",
  "status": "Approved",
  "postPrice": 55000,
  "isHighlighted": true,
  "isAutoRenew": false,
  "expiresAt": "2023-10-15T00:00:00Z",
  "updateRemainingTimes": 5,
  "propertyMedia": [
    {
      "id": "1fa85f64-5717-4562-b3fc-2c963f66afa6",
      "mediaType": "image/jpeg",
      "mediaURL": "https://example.com/property/image1",
      "thumbnailURL": "https://example.com/property/image1?size=thumbnail",
      "smallURL": "https://example.com/property/image1?size=small",
      "mediumURL": "https://example.com/property/image1?size=medium",
      "largeURL": "https://example.com/property/image1?size=large",
      "uploadedAt": "2023-09-15T14:30:00Z",
      "isAvatar": true
    }
  ]
}
```

#### GET /api/Property
Retrieves a list of all properties.

#### GET /api/properties/nearby
Finds properties near a specific geographic location based on latitude, longitude, and radius.

**Query Parameters:**
- latitude: The latitude coordinate of the center point (required)
- longitude: The longitude coordinate of the center point (required)
- radius: Search radius in kilometers (default: 5)
- page: Page number for pagination (default: 1)
- pageSize: Number of items per page (default: 10)

**Response:**
```json
{
  "items": [
    {
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "name": "Apartment near City Center",
      "propertyType": "Apartment",
      "postType": "Rent",
      "address": "123 Main Street",
      "latitude": 10.762622,
      "longitude": 106.660172,
      "price": 15000000,
      "area": 75.5,
      "rooms": 2,
      "toilets": 1,
      "status": "Approved",
      "propertyMedia": [
        {
          "id": "1fa85f64-5717-4562-b3fc-2c963f66afa6",
          "thumbnailURL": "https://example.com/property/image1?size=thumbnail"
        }
      ]
    }
    // ... more properties
  ],
  "totalCount": 15,
  "pageCount": 2,
  "currentPage": 1,
  "pageSize": 10
}
```

#### POST /api/Property
Creates a new property listing.

**Request:**
```json
{
  "ownerId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "name": "Modern Apartment in City Center",
  "propertyType": "Apartment",
  "postType": "Rent",
  "cityId": 1,
  "districtId": 2,
  "wardId": 4,
  "address": "456 Center Street",
  "area": 75.5,
  "price": 15000000,
  "latitude": 10.762622,
  "longitude": 106.660172,
  "videoUrl": "https://example.com/video.mp4",
  "floors": 1,
  "rooms": 2,
  "toilets": 1,
  "direction": "North",
  "balconyDirection": "West",
  "legality": "Sổ hồng",
  "interior": "Cơ bản",
  "description": "Modern apartment with city view",
  "overview": "Centrally located apartment",
  "placeData": "Near public transportation",
  "policies": "No smoking",
  "isHighlighted": false,
  "isAutoRenew": true,
  "uploadedFiles": []
}
```

#### PUT /api/Property/{propertyId}
Updates an existing property listing.

#### DELETE /api/Property/{propertyId}
Deletes a property listing.

#### PUT /api/Property/{propertyId}/status
Updates the status of a property.

**Request:**
```json
{
  "status": "Approved",
  "comment": "Property meets all requirements"
}
```

#### GET /api/Property/search
Searches for properties based on various filters.

**Query Parameters:**
- postType: List of post types (Sale, Rent)
- propertyType: List of property types (House, Apartment, Villa, etc.)
- cityId: City ID
- districtId: District ID
- address: Address text to search
- minPrice: Minimum price
- maxPrice: Maximum price
- minArea: Minimum area
- maxArea: Maximum area
- minRooms: Minimum number of rooms
- minToilets: Minimum number of toilets
- direction: Property direction
- legality: Legal status
- minRoadWidth: Minimum road width
- latitude: Latitude for proximity search
- longitude: Longitude for proximity search
- radius: Radius in kilometers for proximity search
- page: Page number for pagination
- pageSize: Number of items per page

#### POST /api/Property/upload/{propertyId}
Uploads media files for a property.

#### GET /api/Property/history/{propertyId}
Retrieves the status history of a property.

#### GET /api/Property/edit-remaining/{propertyId}
Checks how many edits are remaining for a property.

### 3.3. Logic Flow

#### Property Creation and Publishing Workflow

```mermaid
sequenceDiagram
    participant User
    participant PropertyController
    participant PropertyService
    participant MediaService
    participant Database
    
    User->>PropertyController: Create Property
    PropertyController->>PropertyService: CreatePropertyAsync
    PropertyService->>Database: Store Property
    PropertyService->>Database: Create Status Log (Draft/Pending)
    PropertyService-->>PropertyController: Return PropertyDto
    PropertyController-->>User: Property Created
    
    User->>PropertyController: Upload Images
    PropertyController->>MediaService: Process Images
    PropertyController->>MediaService: Create Media entries
    MediaService->>Database: Store Media References
    MediaService-->>PropertyController: Return Media Info
    PropertyController-->>User: Images Uploaded
    
    User->>PropertyController: Submit for Review
    PropertyController->>PropertyService: UpdateStatusAsync (Pending)
    PropertyService->>Database: Update Property Status
    PropertyService->>Database: Create Status Log
    PropertyService-->>PropertyController: Status Updated
    PropertyController-->>User: Property Submitted
    
    Note over User,Database: Admin Review Process
    
    User->>PropertyController: Check Status History
    PropertyController->>PropertyService: GetPropertyHistoryStatus
    PropertyService->>Database: Fetch Status Logs
    PropertyService-->>PropertyController: Return Status History
    PropertyController-->>User: Display Status History
```

#### Property Search Flow

```mermaid
sequenceDiagram
    participant User
    participant PropertyController
    participant PropertyService
    participant Database
    
    User->>PropertyController: Submit Search Criteria
    PropertyController->>PropertyService: SearchPropertiesAsync
    PropertyService->>Database: Apply Filters
    Note right of PropertyService: Apply geographic filters
    Note right of PropertyService: Apply property attribute filters
    Note right of PropertyService: Apply price range filters
    PropertyService->>Database: Count Total Results
    PropertyService->>Database: Apply Pagination
    Database-->>PropertyService: Return Properties
    PropertyService-->>PropertyController: Return Paged Results
    PropertyController-->>User: Display Search Results
```

#### Nearby Properties Search Flow

```mermaid
sequenceDiagram
    participant User
    participant PropertyController
    participant PropertyService
    participant Database
    
    User->>PropertyController: Send Coordinates (lat, long, radius)
    PropertyController->>PropertyService: SearchPropertiesAsync with coordinates
    PropertyService->>Database: Filter by Haversine Formula
    Note right of PropertyService: Calculate distance between points
    Note right of PropertyService: Filter within specified radius
    PropertyService->>Database: Count Total Results
    PropertyService->>Database: Apply Pagination
    Database-->>PropertyService: Return Nearby Properties
    PropertyService-->>PropertyController: Return Paged Results
    PropertyController-->>User: Display Nearby Properties
```

The Nearby Properties Search utilizes the Haversine formula to calculate the distance between two points on the Earth's surface, taking into account the Earth's curvature. This provides accurate results for proximity-based searches, allowing users to find properties within a specific radius of their current location or a point of interest.

### 3.4. Dependencies

- Entity Framework Core for database operations
- AutoMapper for object mapping between entities and DTOs
- System.Drawing or ImageSharp for image processing
- Spatial libraries for geographic calculations

### 3.5. Security Considerations

- Property data access is protected with role-based authentication
- Property owners can only edit their own listings
- Admin approval process ensures content quality
- Media files are stored securely with proper access controls
- Location data is treated as sensitive information

### 3.6. Performance Considerations

- Property search is optimized with indexed database fields
- Multiple image sizes are generated to optimize bandwidth usage
- Pagination is implemented for all list operations
- Lazy loading of related entities to minimize database load
- Geographic proximity searching is optimized with mathematical calculations

## 4. Testing Plan

- Unit tests for all property service methods
- Integration tests for property CRUD operations and search
- Performance tests for search functionality with large datasets
- UI/UX tests for property creation and management workflows
- Security tests for property access control
- Edge case testing for property status transitions
- Load testing for concurrent property searches and uploads

## 5. Open Questions

- Should we implement a versioning system for property edits to track changes over time?
- How should we handle property data after it's sold or expired? (Archive vs. Delete)
- Should we implement a more sophisticated ranking algorithm for search results?
- What threshold should we set for the proximity search default radius?
- Should automatically expired listings be moved to a separate archive status?

## 6. Alternatives Considered

- Using a dedicated file storage service like AWS S3 instead of local storage for media files was considered but local storage was chosen for simplicity in the initial implementation.
- A more complex workflow for property statuses with additional states was considered but simplified to the current states for user-friendliness.
- A full-text search engine like Elasticsearch was considered for property searches but the current database query approach was chosen for ease of implementation and maintenance.
- Different approaches for handling geographic proximity searches were evaluated, with the current mathematical formula chosen for accuracy and efficiency.
