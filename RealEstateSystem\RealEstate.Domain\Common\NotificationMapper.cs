﻿using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.Domain.Common
{
    public static class NotificationMapper
    {
        public static readonly Dictionary<NotificationType, NotificationCategory> TypeToCategoryMap = new()
        {
            // Listing
            { NotificationType.ListingCreated, NotificationCategory.Listing },
            { NotificationType.ListingApproved, NotificationCategory.Listing },
            { NotificationType.ListingRejected, NotificationCategory.Listing },
            { NotificationType.ListingExpired, NotificationCategory.Listing },
            { NotificationType.NewContactRequest, NotificationCategory.Listing },

            // Finance
            { NotificationType.WalletTopUpSuccess, NotificationCategory.Finance },
            { NotificationType.WalletTopUpFailed, NotificationCategory.Finance },
            { NotificationType.ServicePaymentSuccess, NotificationCategory.Finance },
            { NotificationType.LowBalanceWarning, NotificationCategory.Finance },

            // Promotion
            { NotificationType.NewDiscountAvailable, NotificationCategory.Promotion },
            { NotificationType.PromotionalCodeReceived, NotificationCategory.Promotion },

            // Account
            { NotificationType.WelcomeUser, NotificationCategory.Account },
            { NotificationType.PasswordResetRequest, NotificationCategory.Account },
            { NotificationType.AccountSecurityAlert, NotificationCategory.Account },

            // Miscellaneous
            { NotificationType.SystemMaintenance, NotificationCategory.Miscellaneous },
            { NotificationType.FeatureAnnouncement, NotificationCategory.Miscellaneous },
            { NotificationType.GeneralSystemMessage, NotificationCategory.Miscellaneous }
        };

        public static readonly Dictionary<PropertyStatus, NotificationType> PropertyStatusToNotificationType = new()
        {
             { PropertyStatus.Approved, NotificationType.ListingApproved },
             { PropertyStatus.RejectedByAdmin, NotificationType.ListingRejected },
             { PropertyStatus.PendingApproval, NotificationType.ListingCreated }
        };

        public static string GetNotificationTypeText(NotificationType type)
        {
            return type switch
            {
                NotificationType.ListingCreated => "Tin đăng mới được tạo",
                NotificationType.ListingApproved => "Tin đăng được duyệt",
                NotificationType.ListingRejected => "Tin đăng bị từ chối",
                NotificationType.ListingExpired => "Tin đăng sắp hết hạn / đã hết hạn",
                NotificationType.NewContactRequest => "Có yêu cầu liên hệ mới cho tin đăng",
                NotificationType.WalletTopUpSuccess => "Nạp tiền vào ví thành công",
                NotificationType.WalletTopUpFailed => "Nạp tiền thất bại",
                NotificationType.ServicePaymentSuccess => "Thanh toán dịch vụ thành công",
                NotificationType.LowBalanceWarning => "Cảnh báo số dư sắp hết",
                NotificationType.NewDiscountAvailable => "Có chương trình khuyến mãi mới",
                NotificationType.PromotionalCodeReceived => "Bạn nhận được một mã giảm giá",
                NotificationType.WelcomeUser => "Chào mừng thành viên mới",
                NotificationType.PasswordResetRequest => "Yêu cầu đặt lại mật khẩu",
                NotificationType.AccountSecurityAlert => "Cảnh báo đăng nhập lạ",
                NotificationType.SystemMaintenance => "Thông báo bảo trì hệ thống",
                NotificationType.FeatureAnnouncement => "Thông báo về tính năng mới",
                NotificationType.GeneralSystemMessage => "Thông báo",
                _ => string.Empty
            };
        }
    }
}
