# Database Configuration
POSTGRES_DB=realestate_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password_here

# JWT Configuration
JWT_KEY=your_jwt_secret_key_here_minimum_32_characters_required
JWT_ISSUER=RealEstateAPI
JWT_AUDIENCE=RealEstateUsers

# Internal API JWT Configuration
INTERNAL_JWT_ISSUER=RealEstateInternalAPI
INTERNAL_JWT_AUDIENCE=RealEstateAdmins

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# Storage Configuration
STORAGE_PROVIDER=Local
# STORAGE_PROVIDER=S3

# AWS S3 Configuration (if using S3 storage)
AWS_S3_ACCESS_KEY=your_s3_access_key
AWS_S3_SECRET_KEY=your_s3_secret_key
AWS_S3_REGION=us-east-1
AWS_S3_BUCKET_NAME=your-bucket-name

# AWS SES Configuration (for email)
AWS_SES_ACCESS_KEY=your_ses_access_key
AWS_SES_SECRET_KEY=your_ses_secret_key
AWS_SES_REGION=us-east-1

# Internal Email Configuration
INTERNAL_EMAIL_NOREPLY=<EMAIL>
INTERNAL_EMAIL_SUPPORT=<EMAIL>
INTERNAL_EMAIL_REVIEW=<EMAIL>

# Application Environment
ASPNETCORE_ENVIRONMENT=Production
