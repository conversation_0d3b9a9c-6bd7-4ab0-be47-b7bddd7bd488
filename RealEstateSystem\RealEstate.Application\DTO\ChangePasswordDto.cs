using System.ComponentModel.DataAnnotations;

namespace RealEstate.Application.DTO
{
    public class ChangePasswordDto
    {
        [Required]
        public string Email { get; set; } = string.Empty;
        [Required]
        public string OldPassword { get; set; } = string.Empty;
        [Required]
        public string NewPassword { get; set; } = string.Empty;

        public string GetNormalizedEmail() => Email.Trim().ToLowerInvariant();
    }
}
