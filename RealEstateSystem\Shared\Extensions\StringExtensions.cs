﻿using Slugify;

namespace Shared.Extensions
{
    public static class StringExtensions
    {
        // SlugHelper instance với cấu hình xử lý tiếng Việt
        private static readonly SlugHelper _slugHelper = new SlugHelper(new SlugHelperConfiguration
        {
            StringReplacements = new Dictionary<string, string>
                {
                    { "đ", "d" },
                    { "Đ", "d" }
                }
        });

        /// <summary>
        /// Chuyển chuỗi sang dạng slug URL-safe
        /// </summary>
        /// <param name="input">Chuỗi đầu vào</param>
        /// <returns>Chuỗi slug</returns>
        public static string ToSlug(this string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return string.Empty;

            return _slugHelper.GenerateSlug(input);
        }
    }
}
