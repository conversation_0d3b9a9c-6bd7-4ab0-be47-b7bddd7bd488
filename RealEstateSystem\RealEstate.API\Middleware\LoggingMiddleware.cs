﻿using System.Diagnostics;
using System.Security.Claims;

namespace RealEstate.API.Middleware
{
    public class LoggingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<LoggingMiddleware> _logger;

        public LoggingMiddleware(RequestDelegate next, ILogger<LoggingMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var sw = Stopwatch.StartNew();
            var userId = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "anonymous";
            var traceId = context.TraceIdentifier;

            // Bắt đầu một scope để các log bên trong đều có chung TraceId và UserId
            using (_logger.BeginScope("Request {TraceId} by User {UserId}", traceId, userId))
            {
                // Log thời điểm bắt đầu request
                _logger.LogInformation(
                    "Starting HTTP {RequestMethod} {RequestPath}",
                    context.Request.Method,
                    context.Request.Path);

                // <PERSON>yển request đi tiếp trong pipeline (đây là lúc controller và service chạy)
                await _next(context);

                sw.Stop();

                // Log thời điểm kết thúc request
                // Status code lúc này đã được xác định (bởi Controller hoặc bởi ExceptionMiddleware)
                _logger.LogInformation(
                    "Finished HTTP {RequestMethod} {RequestPath} with status {StatusCode} in {DurationMs}ms.",
                    context.Request.Method,
                    context.Request.Path,
                    context.Response.StatusCode,
                    sw.ElapsedMilliseconds);
            }
        }
    }
}
