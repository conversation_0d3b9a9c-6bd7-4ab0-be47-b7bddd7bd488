﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;

namespace RealEstate.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UserController : BaseController
    {
        private readonly IUserService _userService;
        private readonly IUserDashboardService _dashboardService;
        private readonly ILogger<UserController> _logger;

        public UserController(IUserService userService, IUserDashboardService dashboardService, ILogger<UserController> logger)
        {
            _userService = userService;
            _dashboardService = dashboardService;
            _logger = logger;
        }

        [HttpPut("role")]
        public async Task<IActionResult> AddUserRole(AddUserRoleDto addUserRoleDto)
        {
            var result = await _userService.AddUserRoleAsync(addUserRoleDto);
            return HandleResult(result);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetUser(Guid id)
        {
            var result = await _userService.GetUserByIdAsync(id);
            return HandleResult(result);
        }

        [HttpGet("dashboard")]
        public async Task<IActionResult> GetUserDashboard()
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("User not authenticated");

            var result = await _dashboardService.GetUserDashboardAsync(userId.Value);
            return HandleResult(result);
        }

        [HttpGet("wallet")]
        public async Task<IActionResult> GetUserWallet()
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("User not authenticated");

            var result = await _dashboardService.GetUserWalletInfoAsync(userId.Value);
            return HandleResult(result);
        }

        [HttpGet("properties/stats")]
        public async Task<IActionResult> GetUserPropertyStats()
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("User not authenticated");

            var result = await _dashboardService.GetUserPropertyStatsAsync(userId.Value);
            return HandleResult(result);
        }

        [HttpGet("transactions")]
        public async Task<IActionResult> GetUserTransactions([FromQuery] int count = 10)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("User not authenticated");

            var result = await _dashboardService.GetUserTransactionsAsync(userId.Value, count);
            return HandleResult(result);
        }

        [HttpGet("ranking")]
        public async Task<IActionResult> GetUserRanking()
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("User not authenticated");

            var result = await _dashboardService.GetUserMemberRankingInfoAsync(userId.Value);
            return HandleResult(result);
        }

        [HttpGet("spending/monthly")]
        public async Task<IActionResult> GetMonthlySpending([FromQuery] int year)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("User not authenticated");

            var result = await _dashboardService.GetMonthlySpendingAsync(userId.Value, year);
            return HandleResult(result);
        }

        [HttpGet("properties/performance")]
        public async Task<IActionResult> GetPropertyPerformance()
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("User not authenticated");

            var result = await _dashboardService.GetPropertyPerformanceAsync(userId.Value);
            return HandleResult(result);
        }

        [HttpPost("deactivate")]
        public async Task<IActionResult> DeactivateAccount([FromBody] DeactivateUserDto deactivateUserDto)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("User not authenticated");

            var result = await _userService.DeactivateUserAsync(userId.Value, deactivateUserDto);
            return HandleResult(result);
        }

        [HttpDelete("permanent-delete")]
        public async Task<IActionResult> PermanentDeleteAccount([FromBody] DeactivateUserDto deactivateUserDto)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("User not authenticated");

            var result = await _userService.PermanentDeleteUserAsync(userId.Value, deactivateUserDto);
            return HandleResult(result);
        }

        [HttpGet("tax-info")]
        public async Task<IActionResult> GetUserTaxInfo()
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("User not authenticated");

            var result = await _userService.GetUserInvoiceInfoAsync(userId.Value);
            return HandleResult(result);
        }

        [HttpPut("tax-info")]
        public async Task<IActionResult> UpdateUserTaxInfo([FromBody] UserTaxInfoDto taxInfoDto)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("User not authenticated");

            var result = await _userService.UpdateUserTaxInfoAsync(userId.Value, taxInfoDto);
            return HandleResult(result);
        }
    }
}
