﻿namespace RealEstate.API.Controllers
{
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc;
    using RealEstate.Application.DTO;
    using RealEstate.Application.Interfaces;
    using System.Security.Claims;

    [Route("api/notification-preferences")]
    [ApiController]
    [Authorize] // Only authenticated users should access their preferences
    public class NotificationPreferenceController : BaseController
    {
        private readonly INotificationPreferenceService _notificationPreferenceService;

        public NotificationPreferenceController(INotificationPreferenceService notificationPreferenceService)
        {
            _notificationPreferenceService = notificationPreferenceService;
        }

        // Get notification preferences for the current user
        [HttpGet]
        public async Task<IActionResult> GetPreferences()
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return BadRequest("User không hợp lệ");
            }
            var preferences = await _notificationPreferenceService.GetPreferencesByUserIdAsync(userId.Value);
            return Ok(preferences);
        }

        // Update notification preferences
        [HttpPut]
        public async Task<IActionResult> UpdatePreferences([FromBody] NotificationPreferenceDto preferencesDto)
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return BadRequest("User không hợp lệ");
            }
            var updatedPreferences = await _notificationPreferenceService.UpdatePreferencesAsync(userId.Value, preferencesDto);
            return Ok(updatedPreferences);
        }
    }

}
