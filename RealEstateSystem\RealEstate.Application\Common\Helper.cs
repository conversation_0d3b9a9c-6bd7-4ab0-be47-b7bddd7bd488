﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.Application.Common
{
    public static class Helper
    {
        // Sử dụng NotificationType enum thay vì string
        public static string ConstructActionUrl(
            NotificationType notificationType,
            Guid? propertyId = null,
            Guid? contactRequestId = null,
            Guid? transactionId = null)
        {
            // Switch trực tiếp trên enum, không cần ToLower()
            return notificationType switch
            {
                // ----- <PERSON>h<PERSON>m Tin đăng (Listing) -----
                NotificationType.ListingCreated
                or NotificationType.ListingApproved
                or NotificationType.ListingRejected
                or NotificationType.ListingExpired
                or NotificationType.NewContactRequest when propertyId.HasValue
                    => $"/user/bds/{propertyId}",

                // ----- Nhóm <PERSON> (Finance) -----
                NotificationType.ServicePaymentSuccess
                or NotificationType.WalletTopUpSuccess
                or NotificationType.WalletTopUpFailed when transactionId.HasValue
                    => $"/user/transactions",

                NotificationType.LowBalanceWarning
                    => "/user/wallet",

                // ----- Nhóm Khuyến mãi (Promotion) -----
                NotificationType.NewDiscountAvailable
                or NotificationType.PromotionalCodeReceived
                    => "/promotions",

                // ----- Nhóm Tài khoản (Account) -----
                NotificationType.WelcomeUser
                or NotificationType.PasswordResetRequest
                or NotificationType.AccountSecurityAlert
                    => "/user/profile",

                // ----- Nhóm Khác (Miscellaneous) -----
                NotificationType.FeatureAnnouncement
                or NotificationType.SystemMaintenance
                or NotificationType.GeneralSystemMessage
                    => "/user/notifications", // Dẫn đến trang thông báo chung

                // Trường hợp mặc định cho các loại khác
                _ => "/dashboard"
            };
        }
    }
}
