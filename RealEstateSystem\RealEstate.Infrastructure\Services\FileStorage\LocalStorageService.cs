﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using RealEstate.Application.Interfaces;
using Shared.Enums;
using Shared.Results;

namespace RealEstate.Infrastructure.Services.FileStorage
{
    /// <summary>
    /// Local file system storage implementation
    /// </summary>
    public class LocalStorageService : IFileStorageService
    {
        private readonly ILogger<LocalStorageService> _logger;

        public LocalStorageService(ILogger<LocalStorageService> logger)
        {
            _logger = logger;
        }

        public async Task<Result<string>> UploadFileAsync(IFormFile file, string folder, string fileName)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return Result<string>.Failure("No file provided.", ErrorType.Validation);
                }

                // Ensure folder exists
                var folderResult = await EnsureFolderExistsAsync(folder);
                if (!folderResult.IsSuccess)
                {
                    return Result<string>.Failure(folderResult.ErrorMessage, folderResult.ErrorType ?? ErrorType.Validation);
                }

                var filePath = Path.Combine(folder, fileName);

                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                _logger.LogInformation("File uploaded successfully to {FilePath}", filePath);
                return Result<string>.Success(filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file to {Folder}/{FileName}", folder, fileName);
                return Result<string>.Failure($"Failed to upload file: {ex.Message}", ErrorType.Internal);
            }
        }

        public async Task<Result<string>> UploadFileAsync(byte[] fileBytes, string folder, string fileName, string contentType)
        {
            try
            {
                if (fileBytes == null || fileBytes.Length == 0)
                {
                    return Result<string>.Failure("No file data provided.", ErrorType.Validation);
                }

                // Ensure folder exists
                var folderResult = await EnsureFolderExistsAsync(folder);
                if (!folderResult.IsSuccess)
                {
                    return Result<string>.Failure(folderResult.ErrorMessage, folderResult.ErrorType ?? ErrorType.Validation);
                }

                var filePath = Path.Combine(folder, fileName);

                await File.WriteAllBytesAsync(filePath, fileBytes);

                _logger.LogInformation("File uploaded successfully to {FilePath}", filePath);
                return Result<string>.Success(filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file to {Folder}/{FileName}", folder, fileName);
                return Result<string>.Failure($"Failed to upload file: {ex.Message}", ErrorType.Internal);
            }
        }

        public async Task<Result<string>> MoveFileAsync(string sourcePath, string destinationPath, bool overwrite = true)
        {
            try
            {
                if (!await FileExistsAsync(sourcePath))
                {
                    return Result<string>.Failure("Source file does not exist.", ErrorType.NotFound);
                }

                // Ensure destination directory exists
                var destinationDir = Path.GetDirectoryName(destinationPath);
                if (!string.IsNullOrEmpty(destinationDir))
                {
                    var folderResult = await EnsureFolderExistsAsync(destinationDir);
                    if (!folderResult.IsSuccess)
                    {
                        return folderResult;
                    }
                }

                File.Move(sourcePath, destinationPath, overwrite);

                _logger.LogInformation("File moved from {SourcePath} to {DestinationPath}", sourcePath, destinationPath);
                return Result<string>.Success(destinationPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error moving file from {SourcePath} to {DestinationPath}", sourcePath, destinationPath);
                return Result<string>.Failure($"Failed to move file: {ex.Message}", ErrorType.Internal);
            }
        }

        public async Task<Result> DeleteFileAsync(string filePath)
        {
            try
            {
                if (await FileExistsAsync(filePath))
                {
                    File.Delete(filePath);
                    _logger.LogInformation("File deleted: {FilePath}", filePath);
                }

                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file {FilePath}", filePath);
                return Result.Failure($"Failed to delete file: {ex.Message}", ErrorType.Internal);
            }
        }

        public Task<bool> FileExistsAsync(string filePath)
        {
            return Task.FromResult(File.Exists(filePath));
        }

        public Task<Result<string>> EnsureFolderExistsAsync(string folderPath)
        {
            try
            {
                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath);
                    _logger.LogInformation("Directory created: {FolderPath}", folderPath);
                }

                return Task.FromResult(Result<string>.Success(folderPath));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating directory {FolderPath}", folderPath);
                return Task.FromResult(Result<string>.Failure($"Failed to create directory: {ex.Message}", ErrorType.Internal));
            }
        }

        public string GeneratePublicUrl(string filePath, string scheme, string host)
        {
            // For local storage, we need to extract the file ID from the path
            // The file path format is typically: folder/fileId_size.extension
            var fileName = Path.GetFileNameWithoutExtension(filePath);

            // Extract the GUID part (before any underscore for size variants)
            var fileId = fileName.Split('_')[0];

            return $"{scheme}://{host}/media/{fileId}";
        }

        public string GetPropertyImageFolder(Guid? propertyId)
        {
            if (propertyId != null && propertyId != Guid.Empty)
            {
                return Path.Combine("PropertyImages", propertyId.Value.ToString());
            }
            else
            {
                return Path.Combine("Temp", DateTime.Today.ToString("yyyyMMdd"));
            }
        }

        public string GenerateTempFilePath(string folder, Guid fileId, string extension)
        {
            return Path.Combine(folder, $"temp_{fileId}{extension}");
        }
    }
}
