using RealEstate.Application.DTO;
using Shared.Results;

namespace RealEstate.Application.Interfaces
{
    public interface IUserDashboardService
    {
        Task<Result<UserDashboardDto>> GetUserDashboardAsync(Guid userId);
        Task<Result<WalletInfoDto>> GetUserWalletInfoAsync(Guid userId);
        Task<Result<PropertyStatsDto>> GetUserPropertyStatsAsync(Guid userId);
        Task<Result<List<WalletTransactionDto>>> GetUserTransactionsAsync(Guid userId, int count = 10);
        Task<Result<MemberRankingDto>> GetUserMemberRankingInfoAsync(Guid userId);
        Task<Result<decimal>> GetHighlightFeeByUserIdAsync(Guid userId);

        // Additional methods for optional endpoints
        Task<Result<MonthlySpendingDto>> GetMonthlySpendingAsync(Guid userId, int year);
        Task<Result<PropertyPerformanceDto>> GetPropertyPerformanceAsync(Guid userId);
    }
}