using System.ComponentModel.DataAnnotations;

namespace RealEstate.Application.DTO
{
    public class CreateContactRequestDto
    {
        [Required]
        [MaxLength(255)]
        public string? Name { get; set; }

        [Required]
        [EmailAddress]
        [MaxLength(255)]
        public string? Email { get; set; }

        [Required]
        [MaxLength(20)]
        public string? Phone { get; set; }

        [Required]
        public Guid PropertyId { get; set; }

        [Required]
        public Guid PropertyOwnerId { get; set; } 

        public Guid? UserId { get; set; }

        public string? Note { get; set; }
    }

    public class ContactRequestDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public DateTime SentAt { get; set; }
        public Guid PropertyId { get; set; }
        public Guid PropertyOwnerId { get; set; }
        public Guid? UserId { get; set; }
        public string? Note { get; set; }
        public string? Status { get; set; }
    }

    public class UpdateContactRequestDto
    {
        [Required]
        public Guid Id { get; set; }

        public string? Note { get; set; }

        [Required]
        [MaxLength(20)]
        public string? Status { get; set; }
    }
} 