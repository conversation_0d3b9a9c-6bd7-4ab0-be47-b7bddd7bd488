---
description:
globs:
alwaysApply: false
---
Dưới đây là một bộ template và best practice để ghi log một cách nhất quán tại Controller và Service, đư<PERSON>c tối ưu cho các công cụ giám sát và truy vết lỗi như Datadog, <PERSON><PERSON><PERSON><PERSON>, Elasticsearch (ELK Stack), v.v.

-----

### Nguyên tắc cốt lõi (Core Principles)

1.  **Structured Logging (Log có cấu trúc):** Luôn sử dụng template với các placeholder (`{Key}`) thay vì nối chuỗi. Điều này tạo ra các bản ghi log dạng JSON, gi<PERSON>p các công cụ có thể tìm kiếm, lọc và tạo dashboard một cách dễ dàng.

2.  **Consistency (Tính nhất quán):** C<PERSON>ng một sự kiện phải luôn đư<PERSON> log với cùng một message template và bộ key.

3.  **Correlation (T<PERSON>h tương quan):** Mọi log sinh ra trong cùng một HTTP request phải có một mã định danh chung. Middleware của bạn đã làm rất tốt điều này với `TraceId` và `UserId` thông qua `BeginScope`.

-----

### Template Tổng Quát

#### Định dạng Message

Sử dụng một tiền tố để dễ dàng nhận biết log đến từ tầng nào.
`[{Layer/Component}] {Action/Method} - {EventDescription}`

#### Các Thuộc tính (Properties)

  * **Thuộc tính từ Scope (Middleware):** `TraceId`, `UserId`, `IpAddress` sẽ tự động được đính kèm vào mọi log.
  * **Thuộc tính theo ngữ cảnh:** Các key-value cung cấp chi tiết về sự kiện đang diễn ra.

-----

### I. Log tại Controller (API Layer)

**Mục đích:** Ghi lại **ý định** của người dùng và **kết quả** tổng quan của request. Controller không cần biết chi tiết logic bên trong service.

#### Các điểm log quan trọng:

1.  **Bắt đầu Action:** Ghi lại ý định và các tham số định danh chính (không log toàn bộ DTO).
2.  **Validation thất bại:** Khi dữ liệu đầu vào không hợp lệ.
3.  **Kết quả thành công:** Ghi nhận thành công và ID của tài nguyên vừa được tạo/cập nhật.
4.  **Lỗi nghiệp vụ được xử lý:** Khi service trả về một lỗi đã biết (ví dụ: Not Found, Conflict).

#### Ví dụ Template:

```csharp
// Trong OrdersController.cs
[HttpPost]
public async Task<IActionResult> CreateOrder([FromBody] CreateOrderDto orderDto)
{
    // Điểm 1: Bắt đầu Action
    _logger.LogInformation(
        "[API] CreateOrder - Received request to create order for User {CustomerId}",
        orderDto.CustomerId);

    var result = await _orderService.CreateOrderAsync(orderDto);

    if (!result.IsSuccess)
    {
        // Điểm 4: Lỗi nghiệp vụ được xử lý
        _logger.LogWarning(
            "[API] CreateOrder - Service returned business error for User {CustomerId}. Reason: {Reason}",
            orderDto.CustomerId,
            result.Error);
        return BadRequest(new { Message = result.Error });
    }

    // Điểm 3: Kết quả thành công
    _logger.LogInformation(
        "[API] CreateOrder - Successfully created Order {OrderId} for User {CustomerId}",
        result.Value.Id,
        orderDto.CustomerId);

    return Ok(result.Value);
}
```

-----

### II. Log tại Service (Business Layer)

**Mục đích:** Ghi lại chi tiết các **bước thực thi**, **quyết định logic**, và **tương tác** với các hệ thống bên ngoài (database, API khác).

#### Các điểm log quan trọng:

1.  **Bắt đầu Method:** Ghi lại khi một quy trình nghiệp vụ quan trọng bắt đầu.
2.  **Cột mốc & Quyết định:** Ghi lại các bước chính hoặc các nhánh `if/else` quan trọng.
3.  **Tương tác bên ngoài:** Log **trước khi gọi** và **kết quả** từ một hệ thống khác.
4.  **Thay đổi trạng thái:** Khi một đối tượng quan trọng được tạo hoặc thay đổi trạng thái.
5.  **Lỗi nghiệp vụ cụ thể:** Khi service phát hiện ra một vi phạm quy tắc nghiệp vụ.

#### Ví dụ Template:

```csharp
// Trong OrderService.cs
public async Task<Result<Order>> CreateOrderAsync(CreateOrderDto orderDto)
{
    // Điểm 1: Bắt đầu Method
    _logger.LogInformation(
        "[OrderService] CreateOrderAsync - Starting order creation process for Customer {CustomerId}",
        orderDto.CustomerId);

    // Điểm 2: Cột mốc
    _logger.LogInformation(
        "[OrderService] CreateOrderAsync - Checking inventory for {ItemCount} items.",
        orderDto.Items.Count);
    // ... logic kiểm tra kho ...

    // Điểm 3: Tương tác bên ngoài
    _logger.LogInformation(
        "[OrderService] CreateOrderAsync - Calling PaymentGateway for charge of {Amount}",
        orderDto.TotalAmount);
    var paymentResult = await _paymentGateway.ProcessPaymentAsync(orderDto.TotalAmount);

    if (!paymentResult.IsSuccess)
    {
        // Điểm 5: Lỗi nghiệp vụ cụ thể
        _logger.LogError(
            "[OrderService] CreateOrderAsync - PaymentGateway failed. GatewayResponse: {GatewayResponse}",
            paymentResult.Error);
        return Result.Failure<Order>("Payment failed.");
    }
    _logger.LogInformation(
        "[OrderService] CreateOrderAsync - PaymentGateway successful. TransactionId: {TransactionId}",
        paymentResult.TransactionId);

    // ... logic tạo Order object ...

    // Điểm 4: Thay đổi trạng thái
    _unitOfWork.Orders.Add(order);
    await _unitOfWork.SaveChangesAsync();
    _logger.LogInformation(
        "[OrderService] CreateOrderAsync - Order {OrderId} saved to database for Customer {CustomerId}",
        order.Id,
        order.CustomerId);

    return Result.Success(order);
}
```
