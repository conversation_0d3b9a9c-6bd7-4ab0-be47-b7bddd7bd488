﻿using Npgsql;
using System.Text.Json;

class Program
{
    private const string Path = "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome\\RealEstateSystem\\ImportDataCityLocation\\";
    private const string ConnectionString = "Host=localhost;Port=5432;Database=yezhome;Username=postgres;Password=******";

    static async Task Main()
    {
        try { 
        //await ImportDataAsync(Path + "tinh_tp.json", 
        //    "City", 
        //    new Dictionary<string, string>
        //    {
        //        { "code", "Id" },
        //        { "name", "Name" },
        //        { "slug", "Slug" },
        //        { "type", "Type" },
        //        { "name_with_type", "NameWithType" },
        //    });
        await ImportDataAsync(Path + "quan_huyen.json",
            "District",
            new Dictionary<string, string>
            {
                { "code", "Id" },
                { "name", "Name" },
                { "slug", "Slug" },
                { "type", "Type" },
                { "name_with_type", "NameWithType" },
                { "path", "Path" },
                { "path_with_type", "PathWithType" },
                { "parent_code", "CityId" }
            });
        await ImportDataAsync(Path + "xa_phuong.json",
            "Ward",
            new Dictionary<string, string>
            {
                { "code", "Id" },
                { "name", "Name" },
                { "slug", "Slug" },
                { "type", "Type" },
                { "name_with_type", "NameWithType" },
                { "path", "Path" },
                { "path_with_type", "PathWithType" },
                { "parent_code", "DistrictId" }
            });

        Console.WriteLine($"Imported data DONE");
        Console.ReadLine();
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
            Console.ReadLine();
        }
    }

    private static async Task ImportDataAsync(string jsonPath, string tableName, Dictionary<string, string> columnMappings)
    {
        if (!File.Exists(jsonPath))
        {
            Console.WriteLine($"File not found: {jsonPath}");
            return;
        }

        string jsonContent = await File.ReadAllTextAsync(jsonPath);
        var jsonData = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(jsonContent);

        if (jsonData == null || jsonData.Count == 0)
        {
            Console.WriteLine("No data found in JSON.");
            return;
        }

        await using var connection = new NpgsqlConnection(ConnectionString);
        await connection.OpenAsync();

        foreach (var item in jsonData.Values)
        {
            var columns = new List<string>();
            var values = new List<string>();
            var parameters = new List<NpgsqlParameter>();

            foreach (var (jsonKey, dbColumn) in columnMappings)
            {
                if (item.TryGetValue(jsonKey, out var value))
                {
                    columns.Add($"\"{dbColumn}\"");
                    values.Add($"@{dbColumn}");

                    object parsedValue = value;
                    if (dbColumn == "Id" || dbColumn == "CityId" || dbColumn == "DistrictId")  // Convert Id & CityId & DistrictId to int
                    {
                        if (int.TryParse(value, out int intValue))
                        {
                            parsedValue = intValue;
                        }
                        else
                        {
                            Console.WriteLine($"Invalid integer value for {dbColumn}: {value}");
                            continue;
                        }
                    }


                    parameters.Add(new NpgsqlParameter($"@{dbColumn}", parsedValue));
                }
            }

            if (columns.Count == 0) continue;

            string query = $@"
                INSERT INTO ""{tableName}"" ({string.Join(", ", columns)}) 
                VALUES ({string.Join(", ", values)}) 
                ON CONFLICT DO NOTHING;";

            await using var command = new NpgsqlCommand(query, connection);
            command.Parameters.AddRange(parameters.ToArray());
            await command.ExecuteNonQueryAsync();
        }

        Console.WriteLine($"Imported data into {tableName}");
    }
}

