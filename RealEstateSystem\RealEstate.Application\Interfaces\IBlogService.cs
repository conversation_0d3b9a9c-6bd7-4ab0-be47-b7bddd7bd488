﻿using RealEstate.Application.DTO;
using RealEstate.Domain.CustomModel;
using Shared.Results;

namespace RealEstate.Application.Interfaces
{
    public interface IBlogService
    {
        Task<Result<BlogPostDto>> GetBlogByIdAsync(Guid id);
        Task<Result<BlogPostDto>> GetBlogBySlugAsync(string slug);
        Task<Result<IEnumerable<BlogPostDto>>> GetAllBlogAsync();
        Task<Result<PagedResult<BlogPostDto>>> GetBlogAsync(PagingRequest paging, string title);
        Task<Result<BlogPostDto>> CreateBlogAsync(CreateBlogPostDto BlogDto, Guid userId);
        Task<Result> UpdateBlogAsync(Guid id, CreateBlogPostDto BlogDto, Guid userId);
        Task<Result> DeleteBlogAsync(Guid id, Guid userId);
    }
}
