namespace RealEstate.Application.DTO
{
    public class BulkHighlightResultDto
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public int TotalProcessed { get; set; }
        public int SuccessfullyHighlighted { get; set; }
        public int AlreadyHighlighted { get; set; }
        public int Failed { get; set; }
        public decimal TotalCost { get; set; }
        public List<PropertyHighlightResultDto> PropertyResults { get; set; } = new List<PropertyHighlightResultDto>();
    }

    public class PropertyHighlightResultDto
    {
        public Guid PropertyId { get; set; }
        public string? PropertyTitle { get; set; }
        public bool Success { get; set; }
        public string? Status { get; set; } // "highlighted", "already_highlighted", "failed"
        public string? ErrorMessage { get; set; }
        public decimal Cost { get; set; }
        public Guid? InvoiceId { get; set; }
        public Guid? TransactionId { get; set; }

        // Additional properties for single property operations
        public string? Message { get; set; }
        public bool IsHighlighted { get; set; }
        public PropertyDto? Property { get; set; }
    }
}
