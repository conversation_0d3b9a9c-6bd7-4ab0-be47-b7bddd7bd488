﻿using RealEstate.Application.Interfaces;

namespace RealEstate.Infrastructure.Services.PaymentGateway
{
    public class FakePaymentGatewayService : IPaymentGatewayService
    {
        public Task<GatewayResponse> CreatePaymentRequestAsync(GatewayRequest request)
        {
            // Thay vì gọi <PERSON>/VNPay, chúng ta tạo một URL trỏ về trang giả lập trên Next.js
            // Trang này sẽ nhận mã giao dịch để có thể kích hoạt kết quả sau đó.
            var fakePaymentUrl = $"/mock-fake/wallet/fake-payment?transactionReference={request.TransactionReference}&amount={request.Amount}";

            var response = new GatewayResponse
            {
                IsSuccess = true,
                PaymentUrl = fakePaymentUrl
            };

            return Task.FromResult(response);
        }
    }
}
