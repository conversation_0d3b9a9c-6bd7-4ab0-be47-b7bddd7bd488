using AutoMapper;
using Microsoft.EntityFrameworkCore;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Common;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using Shared.Enums;
using Shared.Results;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RealEstate.Application.Services
{
    public class UserDashboardService : IUserDashboardService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public UserDashboardService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<Result<UserDashboardDto>> GetUserDashboardAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null)
            {
                return Result<UserDashboardDto>.Failure($"User with ID {userId} not found", ErrorType.NotFound);
            }

            var userInfoResult = await GetUserInfoAsync(userId);
            var walletInfoResult = await GetUserWalletInfoAsync(userId);
            var propertyStatsResult = await GetUserPropertyStatsAsync(userId);
            var transactionsResult = await GetUserTransactionsAsync(userId, 5);
            var rankingResult = await GetUserMemberRankingInfoAsync(userId);

            var dashboard = new UserDashboardDto
            {
                UserInfo = userInfoResult.IsSuccess ? userInfoResult.Value : null,
                WalletInfo = walletInfoResult.IsSuccess ? walletInfoResult.Value : null,
                PropertyStats = propertyStatsResult.IsSuccess ? propertyStatsResult.Value : null,
                RecentTransactions = transactionsResult.IsSuccess ? transactionsResult.Value : new List<WalletTransactionDto>(),
                MemberRanking = rankingResult.IsSuccess ? rankingResult.Value : null
            };

            return Result<UserDashboardDto>.Success(dashboard);
        }

        private async Task<Result<UserInfoDto>> GetUserInfoAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if(user == null) return Result<UserInfoDto>.Failure("User not found", ErrorType.NotFound);

            var userRoles = await _unitOfWork.UserRoles.GetQueryable()
                .Where(ur => ur.UserID == userId)
                .Select(ur => ur.Role.RoleName)
                .ToListAsync();

            var userInfo = new UserInfoDto
            {
                Id = user.Id,
                FullName = user.FullName,
                Email = user.Email,
                Phone = user.Phone,
                UserType = user.UserType,
                MemberRank = user.MemberRank,
                LastLogin = user.LastLogin,
                CreatedAt = user.CreatedAt,
                Roles = userRoles
            };
            return Result<UserInfoDto>.Success(userInfo);
        }

        public async Task<Result<WalletInfoDto>> GetUserWalletInfoAsync(Guid userId)
        {
            var wallet = await _unitOfWork.Wallets.GetQueryable().FirstOrDefaultAsync(w => w.UserId == userId);
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if(user == null) return Result<WalletInfoDto>.Failure("User not found", ErrorType.NotFound);

            var walletInfo = new WalletInfoDto
            {
                Balance = wallet?.Balance ?? 0,
                TotalSpent = user.TotalSpent
            };
            return Result<WalletInfoDto>.Success(walletInfo);
        }

        public async Task<Result<PropertyStatsDto>> GetUserPropertyStatsAsync(Guid userId)
        {
            var properties = await _unitOfWork.Properties.GetQueryable()
                .Where(p => p.OwnerID == userId && !p.IsDeleted)
                .ToListAsync();
            
            var stats = new PropertyStatsDto
            {
                TotalProperties = properties.Count,
                ActiveProperties = properties.Count(p => p.Status == EnumValues.PropertyStatus.Approved),
                ExpiredProperties = properties.Count(p => p.ExpiresAt <= DateTime.UtcNow),
                DraftProperties = properties.Count(p => p.Status == EnumValues.PropertyStatus.Draft),
                FavoriteProperties = await _unitOfWork.UserFavorites.GetQueryable().CountAsync(f => f.UserID == userId),
                TotalViews = 0, // Placeholder
            };
            return Result<PropertyStatsDto>.Success(stats);
        }

        public async Task<Result<List<WalletTransactionDto>>> GetUserTransactionsAsync(Guid userId, int count = 10)
        {
            var transactions = await _unitOfWork.WalletTransactions.GetQueryable()
                .Where(wt => wt.UserId == userId)
                .OrderByDescending(wt => wt.CreatedAt)
                .Take(count)
                .ToListAsync();
            
            return Result<List<WalletTransactionDto>>.Success(_mapper.Map<List<WalletTransactionDto>>(transactions));
        }

        public async Task<Result<MemberRankingDto>> GetUserMemberRankingInfoAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if(user == null) return Result<MemberRankingDto>.Failure("User not found", ErrorType.NotFound);

            var currentRanking = await _unitOfWork.MemberRankings.GetQueryable()
                .FirstOrDefaultAsync(r => r.RankName == user.MemberRank);

            // Find the next rank based on spending thresholds
            var nextRanking = await _unitOfWork.MemberRankings.GetQueryable()
                .Where(r => r.MinSpent > user.TotalSpent)
                .OrderBy(r => r.MinSpent)
                .FirstOrDefaultAsync();

            decimal progressPercentage = 0;
            decimal? spendingToNextRank = null;

            if (nextRanking != null && currentRanking != null && currentRanking.MinSpent.HasValue && nextRanking.MinSpent.HasValue)
            {
                spendingToNextRank = nextRanking.MinSpent - user.TotalSpent;

                var rankingRange = nextRanking.MinSpent.Value - currentRanking.MinSpent.Value;
                var userProgress = user.TotalSpent - currentRanking.MinSpent.Value;

                progressPercentage = rankingRange > 0
                    ? Math.Min(100, (userProgress / rankingRange) * 100)
                    : 100;
            }
            else if (currentRanking != null && currentRanking.MaxSpent.HasValue)
            {
                // User is at the maximum rank
                progressPercentage = 100;
            }

            var rankingDto = new MemberRankingDto
            {
                CurrentRank = user.MemberRank,
                NextRank = nextRanking?.RankName,
                SpendingToNextRank = spendingToNextRank,
                MinSpent = currentRanking?.MinSpent,
                MaxSpent = currentRanking?.MaxSpent,
                ProgressPercentage = (double)progressPercentage
            };
            return Result<MemberRankingDto>.Success(rankingDto);
        }

        public async Task<Result<MonthlySpendingDto>> GetMonthlySpendingAsync(Guid userId, int year)
        {
            // Get all transactions for the user in the specified year
            var startDate = new DateTime(year, 1, 1);
            var endDate = new DateTime(year, 12, 31, 23, 59, 59);

            var transactions = await _unitOfWork.WalletTransactions.GetQueryable()
                .Where(wt => wt.UserId == userId &&
                       wt.CreatedAt >= startDate &&
                       wt.CreatedAt <= endDate)
                .ToListAsync();

            // Group transactions by month
            var groupedByMonth = transactions
                .GroupBy(t => t.CreatedAt.Month)
                .Select(g => new
                {
                    Month = g.Key,
                    Transactions = g.ToList()
                })
                .ToList();

            // Create monthly spending details
            var monthlyDetails = new List<MonthlySpendingDetailDto>();
            for (int month = 1; month <= 12; month++)
            {
                var monthData = groupedByMonth.FirstOrDefault(g => g.Month == month);
                var spendTransactions = monthData?.Transactions.Where(t => t.Type == "spend").ToList() ?? new List<WalletTransaction>();

                monthlyDetails.Add(new MonthlySpendingDetailDto
                {
                    Month = month,
                    Name = new DateTime(year, month, 1).ToString("MMMM"),
                    TotalSpent = spendTransactions.Sum(t => t.Amount),
                    TransactionCount = spendTransactions.Count
                });
            }

            // Calculate total yearly spending
            var totalYearlySpending = monthlyDetails.Sum(m => m.TotalSpent);

            var dto = new MonthlySpendingDto
            {
                Year = year,
                Months = monthlyDetails,
                TotalYearlySpending = totalYearlySpending
            };
            return Result<MonthlySpendingDto>.Success(dto);
        }

        public async Task<Result<decimal>> GetHighlightFeeByUserIdAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return Result<decimal>.Failure("User not found", ErrorType.NotFound);

            var highlightFee = await _unitOfWork.HighlightFees.GetQueryable()
                .FirstOrDefaultAsync(f => f.RankName == user.MemberRank);

            if (highlightFee == null)
            {
                highlightFee = await _unitOfWork.HighlightFees.GetQueryable()
                    .FirstOrDefaultAsync(f => f.RankName == "default");

                if (highlightFee == null)
                {
                    return Result<decimal>.Failure("Could not determine highlight fee", ErrorType.NotFound);
                }
            }

            return Result<decimal>.Success(highlightFee.Fee);
        }

        public async Task<Result<PropertyPerformanceDto>> GetPropertyPerformanceAsync(Guid userId)
        {
            var properties = await _unitOfWork.Properties.GetQueryable()
                .Where(p => p.OwnerID == userId && !p.IsDeleted)
                .ToListAsync();

            if (properties == null || !properties.Any())
            {
                return Result<PropertyPerformanceDto>.Success(new PropertyPerformanceDto
                {
                    TotalProperties = 0,
                    PropertiesPerformance = new List<PropertyPerformanceDetailDto>(),
                    BestPerforming = null,
                    NeedsAttention = new List<PropertyAttentionDto>()
                });
            }

            // Get favorites count for each property
            var favorites = await _unitOfWork.UserFavorites.GetQueryable()
                .Where(f => properties.Select(p => p.Id).Contains(f.PropertyID))
                .GroupBy(f => f.PropertyID)
                .Select(g => new { PropertyId = g.Key, Count = g.Count() })
                .ToListAsync();

            // Get contact requests count for each property
            // Note: You would need to implement this based on your contact request model
            // This is a placeholder implementation
            var contactRequests = new Dictionary<Guid, int>();
            foreach (var property in properties)
            {
                contactRequests[property.Id] = 0; // Replace with actual contact request query
            }

            // Prepare property performance details
            var performanceDetails = new List<PropertyPerformanceDetailDto>();
            var needsAttention = new List<PropertyAttentionDto>();

            var now = DateTime.UtcNow;

            foreach (var property in properties)
            {
                var favoriteCount = favorites.FirstOrDefault(f => f.PropertyId == property.Id)?.Count ?? 0;
                var contactRequestCount = contactRequests.ContainsKey(property.Id) ? contactRequests[property.Id] : 0;

                // Add to performance details
                performanceDetails.Add(new PropertyPerformanceDetailDto
                {
                    PropertyId = property.Id,
                    PropertyName = property.Name,
                    Views = 0, // Replace with actual view count if you track it
                    Favorites = favoriteCount,
                    ContactRequests = contactRequestCount,
                    Status = property.Status,
                    ExpiresAt = property.ExpiresAt,
                });

                // Check if property needs attention
                if (property.ExpiresAt <= now.AddDays(7))
                {
                    needsAttention.Add(new PropertyAttentionDto
                    {
                        PropertyId = property.Id,
                        PropertyName = property.Name,
                        Issue = "Expires soon",
                        ExpiresAt = property.ExpiresAt
                    });
                }
            }

            // Find best performing property (based on a simple scoring mechanism)
            var bestProperty = performanceDetails
                .OrderByDescending(p => p.Views + p.Favorites * 2 + p.ContactRequests * 3 + (p.Rating * p.ReviewCount))
                .FirstOrDefault();

            var bestPerforming = bestProperty != null
                ? new BestPerformingPropertyDto
                {
                    PropertyId = bestProperty.PropertyId,
                    PropertyName = bestProperty.PropertyName
                }
                : null;

            return Result<PropertyPerformanceDto>.Success(new PropertyPerformanceDto
            {
                TotalProperties = properties.Count,
                PropertiesPerformance = performanceDetails,
                BestPerforming = bestPerforming,
                NeedsAttention = needsAttention
            });
        }
    }
}