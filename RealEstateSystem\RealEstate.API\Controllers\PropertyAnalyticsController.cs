using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.API.Attributes;
using RealEstate.API.DTO;
using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Analytics;
using RealEstate.Application.Interfaces;

namespace RealEstate.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class PropertyAnalyticsController : BaseController
    {
        private readonly IPropertyAnalyticsService _analyticsService;
        private readonly IPropertyService _propertyService;

        public PropertyAnalyticsController(
            IPropertyAnalyticsService analyticsService,
            IPropertyService propertyService)
        {
            _analyticsService = analyticsService;
            _propertyService = propertyService;
        }

        [HttpGet("property/{propertyId}")]
        public async Task<IActionResult> GetPropertyAnalytics(
            Guid propertyId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            if (GetUserId() == null) return Unauthorized("User not authenticated");

            var result = await _analyticsService.GetPropertyAnalyticsAsync(propertyId, startDate, endDate);
            return HandleResult(result);
        }

        [HttpGet("user")]
        public async Task<IActionResult> GetUserPropertiesAnalytics(
            [FromQuery] PropertyAnalyticsFilterDto filter)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("User not authenticated");

            var result = await _analyticsService.GetUserPropertiesAnalyticsAsync(userId.Value, filter);
            return HandleResult(result);
        }

        [HttpGet("property/{propertyId}/export")]
        public async Task<IActionResult> ExportPropertyAnalytics(
            Guid propertyId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            if (GetUserId() == null) return Unauthorized("User not authenticated");

            var result = await _analyticsService.ExportPropertyAnalyticsToExcelAsync(propertyId, startDate, endDate);
            if (!result.IsSuccess) return HandleResult(result);
            return File(result.Value, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"property_analytics_{propertyId}.xlsx");
        }

        [HttpGet("user/export")]
        public async Task<IActionResult> ExportUserPropertiesAnalytics(
            [FromQuery] PropertyAnalyticsFilterDto filter)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("User not authenticated");
            
            var result = await _analyticsService.ExportUserPropertiesAnalyticsToExcelAsync(userId.Value, filter);
            if (!result.IsSuccess) return HandleResult(result);
            return File(result.Value, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "user_properties_analytics.xlsx");
        }

        [HttpPost("property/{propertyId}/view")]
        [AllowAnonymous]
        public async Task<IActionResult> LogPropertyView(Guid propertyId, [FromBody] LogPropertyViewRequest request)
        {
            var dto = new LogPropertyViewDto
            {
                PropertyId = propertyId,
                ViewerId = GetUserId(),
                ViewerIp = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown",
                UserAgent = request.UserAgent ?? Request.Headers["User-Agent"].ToString(),
                ReferrerUrl = Request.Headers["Referer"].ToString(),
                SessionId = request.SessionId,
                DeviceId = request.DeviceId
            };
            var result = await _analyticsService.LogPropertyViewAsync(dto);
            return HandleResult(result);
        }

        /// <summary>
        /// Get the status history of a property
        /// </summary>
        /// <param name="propertyId">The ID of the property</param>
        /// <returns>List of property status history entries</returns>
        /// <response code="200">Returns the property history</response>
        /// <response code="401">If the user is not authenticated</response>
        [HttpGet("history/status/{propertyId}")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> GetPropertyHistoryStatus(Guid propertyId)
        {
            var result = await _analyticsService.GetPropertyHistoryStatus(propertyId);
            return HandleResult(result);
        }

        /// <summary>
        /// Log property engagement event (matches frontend /api/log/property-event route)
        /// </summary>
        [HttpPost("/api/log/property-event")]
        [ServiceFilter(typeof(EnforceFrontendOriginAttribute))]
        [AllowAnonymous]
        public async Task<IActionResult> LogPropertyEvent([FromBody] LogPropertyEventRequest request)
        {
            if (!Guid.TryParse(request.PropertyId, out var propertyId))
            {
                return BadRequest("Invalid property ID format");
            }

            var dto = new LogPropertyEngagementEventDto
            {
                PropertyId = propertyId,
                UserId = GetUserId(),
                EventType = request.EventType,
                SessionId = request.SessionId,
                DeviceId = request.DeviceId,
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown",
                UserAgent = request.UserAgent ?? Request.Headers["User-Agent"].ToString()
            };

            var result = await _analyticsService.LogPropertyEngagementEventAsync(dto);
            return HandleResult(result);
        }

        /// <summary>
        /// Log property view (matches frontend /api/log/property-view route)
        /// </summary>
        [HttpPost("/api/log/property-view")]
        [ServiceFilter(typeof(EnforceFrontendOriginAttribute))]
        [AllowAnonymous]
        public async Task<IActionResult> LogPropertyViewFromFrontend([FromBody] LogPropertyViewRequest request)
        {
            if (!Guid.TryParse(request.PropertyId, out var propertyId))
            {
                return BadRequest("Invalid property ID format");
            }

            var dto = new LogPropertyViewDto
            {
                PropertyId = propertyId,
                ViewerId = GetUserId(),
                ViewerIp = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown",
                UserAgent = request.UserAgent ?? Request.Headers["User-Agent"].ToString(),
                ReferrerUrl = Request.Headers["Referer"].ToString(),
                SessionId = request.SessionId,
                DeviceId = request.DeviceId
            };

            var result = await _analyticsService.LogPropertyViewAsync(dto);
            return HandleResult(result);
        }

        /// <summary>
        /// Get property engagement summary by property ID
        /// </summary>
        /// <param name="propertyId">The ID of the property</param>
        /// <returns>Property engagement summary data</returns>
        /// <response code="200">Returns the property engagement summary</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="404">If the property is not found</response>
        [HttpGet("property/{propertyId}/summary")]
        [Authorize]
        public async Task<IActionResult> GetPropertyEngagementSummary(Guid propertyId)
        {
            if (GetUserId() == null) return Unauthorized("User not authenticated");

            var result = await _analyticsService.GetPropertyEngagementSummaryAsync(propertyId);
            return HandleResult(result);
        }


    }
}
