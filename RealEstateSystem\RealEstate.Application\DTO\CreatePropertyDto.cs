﻿using System.ComponentModel.DataAnnotations;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.Application.DTO
{
    public class CreatePropertyDto
    {
        [Required]
        public Guid? OwnerId { get; set; }
        [Required]
        public required string Name { get; set; }
        [Required]
        public PropertyType PropertyType { get; set; }
        [Required]
        public PostType PostType { get; set; }
        [Required]
        public int CityId { get; set; }
        [Required]
        public int DistrictId { get; set; }
        public int? StreetId { get; set; }
        [Required]
        public int WardId { get; set; }
        [Required]
        public required string Address { get; set; }
        public decimal? Area { get; set; }
        [Required]
        public decimal Price { get; set; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public string? VideoUrl { get; set; }
        public int? Floors { get; set; }
        public int? Rooms { get; set; }
        public int? Toilets { get; set; }
        public string? Direction { get; set; }
        public string? BalconyDirection { get; set; }
        public string? Legality { get; set; }
        public string? Interior { get; set; }
        public int? Width { get; set; }
        public int? RoadWidth { get; set; }
        [Required]
        public required string Description { get; set; }
        public string? Overview { get; set; }
        public string? PlaceData { get; set; }
        public string? Policies { get; set; }
        public string? Neighborhood { get; set; }
        [Required]
        public required PropertyStatus Status { get; set; } = PropertyStatus.Draft;
        public bool IsHighlighted { get; set; } = false;
        public bool IsAutoRenew { get; set; } = false;

        public List<PropertyMediaDto>? UploadedFiles { get; set; } = new List<PropertyMediaDto>();
    }

    public class UpdateStatusDto
    {
        [Required]
        public required PropertyStatus Status { get; set; }
        public string? Comment { get; set; }
    }
}
