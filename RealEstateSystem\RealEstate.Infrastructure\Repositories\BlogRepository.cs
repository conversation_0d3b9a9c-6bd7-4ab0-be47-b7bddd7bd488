﻿using Microsoft.EntityFrameworkCore;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace RealEstate.Infrastructure.Repositories
{
    public class BlogRepository : AuditableRepository<BlogPost>, IBlogRepository
    {
        public BlogRepository(ApplicationDbContext context) : base(context) {}

        public async Task<IEnumerable<BlogPost>> GetBlogPostByAuthorAsync(Guid authorId)
        {
            return await _dbSet
            .Where(bp => bp.AuthorID == authorId && !bp.IsDeleted)
            .ToListAsync();
        }

        public async Task<BlogPost?> GetBlogPostBySlugAsync(string slug)
        {
            return await _dbSet.FirstOrDefaultAsync(bp => bp.Slug == slug && !bp.IsDeleted);
        }

        public async Task<IEnumerable<BlogPost>> GetBlogPostByDateAsync(DateTime? from = null, DateTime? to = null)
        {
            var query = _dbSet.AsQueryable();

            // Filter out soft-deleted blog posts
            query = query.Where(bp => !bp.IsDeleted && bp.PublishedAt.HasValue);

            // Apply the 'from' date filter
            if (from.HasValue)
            {
                query = query.Where(bp => bp.PublishedAt.HasValue && bp.PublishedAt.Value.Date >= from.Value.Date);
            }

            // Apply the 'to' date filter
            if (to.HasValue)
            {
                query = query.Where(bp => bp.PublishedAt.HasValue && bp.PublishedAt.Value.Date <= to.Value.Date);
            }

            return await query.ToListAsync();
        }
    }
}
