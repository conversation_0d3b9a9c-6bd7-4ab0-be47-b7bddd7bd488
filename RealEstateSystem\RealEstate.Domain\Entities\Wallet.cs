﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RealEstate.Domain.Entities
{
    public class Wallet : BaseEntity
    {

        [ForeignKey("AppUser")]
        public Guid UserId { get; set; }

        [Column(TypeName = "numeric(20,2)")]
        public decimal Balance { get; set; } = 0;

        public uint RowVersion { get; set; }

        public virtual AppUser User { get; set; }
    }
}
