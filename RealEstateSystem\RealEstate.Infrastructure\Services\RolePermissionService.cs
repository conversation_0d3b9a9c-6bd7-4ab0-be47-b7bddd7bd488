using AutoMapper;
using Microsoft.EntityFrameworkCore;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using Shared.Constants;
using Shared.Enums;
using Shared.Results;

namespace RealEstate.Application.Services
{
    public class RolePermissionService : IRolePermissionService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public RolePermissionService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        #region Role Management

        /// <summary>
        /// Get all roles in the system
        /// </summary>
        /// <returns>List of all roles</returns>
        public async Task<Result<IEnumerable<AdminRoleDto>>> GetAllRolesAsync()
        {
            var roles = await _unitOfWork.AdminRoles.GetQueryable()
                .Include(r => r.RolePermissions)
                .ThenInclude(rp => rp.Permission)
                .ToListAsync();

            return Result<IEnumerable<AdminRoleDto>>.Success(_mapper.Map<IEnumerable<AdminRoleDto>>(roles));
        }

        /// <summary>
        /// Get role by ID with its permissions
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <returns>Role with permissions</returns>
        public async Task<Result<AdminRoleDto>> GetRoleByIdAsync(Guid roleId)
        {
            var role = await _unitOfWork.AdminRoles.GetQueryable()
                .Include(r => r.RolePermissions)
                .ThenInclude(rp => rp.Permission)
                .FirstOrDefaultAsync(r => r.Id == roleId);

            if (role == null)
                return Result<AdminRoleDto>.Failure(RolePermissionMessage.RoleNotFound, ErrorType.NotFound);

            return Result<AdminRoleDto>.Success(_mapper.Map<AdminRoleDto>(role));
        }

        /// <summary>
        /// Create a new role
        /// </summary>
        /// <param name="createRoleDto">Role creation data</param>
        /// <returns>Created role</returns>
        public async Task<Result<AdminRoleDto>> CreateRoleAsync(CreateAdminRoleDto createRoleDto)
        {
            // Check if role code already exists
            var existingRole = await _unitOfWork.AdminRoles.GetQueryable()
                .FirstOrDefaultAsync(r => r.Code == createRoleDto.Code);

            if (existingRole != null)
                return Result<AdminRoleDto>.Failure(RolePermissionMessage.RoleCodeAlreadyExists, ErrorType.Conflict);

            var role = new AdminRole
            {
                RoleName = createRoleDto.RoleName,
                Code = createRoleDto.Code
            };

            await _unitOfWork.AdminRoles.AddAsync(role);
            await _unitOfWork.SaveChangesAsync();

            return Result<AdminRoleDto>.Success(_mapper.Map<AdminRoleDto>(role));
        }

        /// <summary>
        /// Update an existing role
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <param name="updateRoleDto">Role update data</param>
        /// <returns>Updated role</returns>
        public async Task<Result<AdminRoleDto>> UpdateRoleAsync(Guid roleId, CreateAdminRoleDto updateRoleDto)
        {
            var role = await _unitOfWork.AdminRoles.GetByIdAsync(roleId);
            if (role == null)
                return Result<AdminRoleDto>.Failure(RolePermissionMessage.RoleNotFound, ErrorType.NotFound);

            // Check if the new code conflicts with another role
            if (role.Code != updateRoleDto.Code)
            {
                var existingRole = await _unitOfWork.AdminRoles.GetQueryable()
                    .FirstOrDefaultAsync(r => r.Code == updateRoleDto.Code && r.Id != roleId);

                if (existingRole != null)
                    return Result<AdminRoleDto>.Failure(RolePermissionMessage.RoleCodeAlreadyExists, ErrorType.Conflict);
            }

            role.RoleName = updateRoleDto.RoleName;
            role.Code = updateRoleDto.Code;

            _unitOfWork.AdminRoles.Update(role);
            await _unitOfWork.SaveChangesAsync();

            return Result<AdminRoleDto>.Success(_mapper.Map<AdminRoleDto>(role));
        }

        /// <summary>
        /// Delete a role (only if no users are assigned to it)
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <returns>True if deleted successfully</returns>
        public async Task<Result> DeleteRoleAsync(Guid roleId)
        {
            var role = await _unitOfWork.AdminRoles.GetQueryable()
                .Include(r => r.UserRoles)
                .FirstOrDefaultAsync(r => r.Id == roleId);

            if (role == null)
                return Result.Failure(RolePermissionMessage.RoleNotFound, ErrorType.NotFound);

            // Check if any users are assigned to this role
            if (role.UserRoles.Any())
                return Result.Failure(RolePermissionMessage.CannotDeleteRoleWithAssignedUsers, ErrorType.Conflict);

            // Remove all role permissions first
            var rolePermissions = await _unitOfWork.RolePermissions.GetQueryable()
                .Where(rp => rp.RoleID == roleId)
                .ToListAsync();

            foreach (var rolePermission in rolePermissions)
            {
                _unitOfWork.RolePermissions.Remove(rolePermission);
            }

            _unitOfWork.AdminRoles.Remove(role);
            await _unitOfWork.SaveChangesAsync();

            return Result.Success();
        }

        #endregion

        #region Permission Management

        /// <summary>
        /// Get all permissions in the system
        /// </summary>
        /// <returns>List of all permissions</returns>
        public async Task<Result<IEnumerable<PermissionDto>>> GetAllPermissionsAsync()
        {
            var permissions = await _unitOfWork.Permissions.GetAllAsync();
            return Result<IEnumerable<PermissionDto>>.Success(_mapper.Map<IEnumerable<PermissionDto>>(permissions));
        }

        /// <summary>
        /// Get permissions assigned to a specific role
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <returns>List of permissions for the role</returns>
        public async Task<Result<IEnumerable<PermissionDto>>> GetRolePermissionsAsync(Guid roleId)
        {
            var rolePermissions = await _unitOfWork.RolePermissions.GetQueryable()
                .Where(rp => rp.RoleID == roleId)
                .Include(rp => rp.Permission)
                .ToListAsync();

            var permissions = rolePermissions.Select(rp => rp.Permission).ToList();
            return Result<IEnumerable<PermissionDto>>.Success(_mapper.Map<IEnumerable<PermissionDto>>(permissions));
        }

        #endregion

        #region Role Permission Assignment

        /// <summary>
        /// Assign permissions to a role (replaces existing permissions)
        /// </summary>
        /// <param name="assignPermissionsDto">Role and permission assignment data</param>
        /// <returns>True if successful</returns>
        public async Task<Result> AssignPermissionsToRoleAsync(AssignPermissionsToRoleDto assignPermissionsDto)
        {
            // Verify role exists
            var role = await _unitOfWork.AdminRoles.GetByIdAsync(assignPermissionsDto.RoleID);
            if (role == null)
                return Result.Failure(RolePermissionMessage.RoleNotFound, ErrorType.NotFound);

            // Verify all permissions exist
            foreach (var permissionId in assignPermissionsDto.PermissionIDs)
            {
                var permission = await _unitOfWork.Permissions.GetByIdAsync(permissionId);
                if (permission == null)
                    return Result.Failure(RolePermissionMessage.PermissionNotFound, ErrorType.NotFound);
            }

            // Remove existing role permissions
            var existingRolePermissions = await _unitOfWork.RolePermissions.GetQueryable()
                .Where(rp => rp.RoleID == assignPermissionsDto.RoleID)
                .ToListAsync();

            foreach (var existingRolePermission in existingRolePermissions)
            {
                _unitOfWork.RolePermissions.Remove(existingRolePermission);
            }

            // Add new role permissions
            foreach (var permissionId in assignPermissionsDto.PermissionIDs)
            {
                var roleEntity = await _unitOfWork.AdminRoles.GetByIdAsync(assignPermissionsDto.RoleID);
                var permissionEntity = await _unitOfWork.Permissions.GetByIdAsync(permissionId);

                var rolePermission = new RolePermission
                {
                    RoleID = assignPermissionsDto.RoleID,
                    PermissionID = permissionId,
                    Role = roleEntity!,
                    Permission = permissionEntity!
                };
                await _unitOfWork.RolePermissions.AddAsync(rolePermission);
            }

            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        /// <summary>
        /// Add a single permission to a role
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <param name="permissionId">Permission ID</param>
        /// <returns>True if successful</returns>
        public async Task<Result> AddPermissionToRoleAsync(Guid roleId, Guid permissionId)
        {
            // Verify role and permission exist
            var role = await _unitOfWork.AdminRoles.GetByIdAsync(roleId);
            if (role == null)
                return Result.Failure(RolePermissionMessage.RoleNotFound, ErrorType.NotFound);

            var permission = await _unitOfWork.Permissions.GetByIdAsync(permissionId);
            if (permission == null)
                return Result.Failure(RolePermissionMessage.PermissionNotFound, ErrorType.NotFound);

            // Check if permission is already assigned to role
            var existingRolePermission = await _unitOfWork.RolePermissions.GetQueryable()
                .FirstOrDefaultAsync(rp => rp.RoleID == roleId && rp.PermissionID == permissionId);

            if (existingRolePermission != null)
                return Result.Success(); // Already assigned, no need to add again

            var rolePermission = new RolePermission
            {
                RoleID = roleId,
                PermissionID = permissionId,
                Role = role,
                Permission = permission
            };

            await _unitOfWork.RolePermissions.AddAsync(rolePermission);
            await _unitOfWork.SaveChangesAsync();

            return Result.Success();
        }

        /// <summary>
        /// Remove a permission from a role
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <param name="permissionId">Permission ID</param>
        /// <returns>True if successful</returns>
        public async Task<Result> RemovePermissionFromRoleAsync(Guid roleId, Guid permissionId)
        {
            var rolePermission = await _unitOfWork.RolePermissions.GetQueryable()
                .FirstOrDefaultAsync(rp => rp.RoleID == roleId && rp.PermissionID == permissionId);

            if (rolePermission == null)
                return Result.Failure(RolePermissionMessage.RolePermissionRemovalFailed, ErrorType.NotFound); // Permission not assigned to role, or not found

            _unitOfWork.RolePermissions.Remove(rolePermission);
            await _unitOfWork.SaveChangesAsync();

            return Result.Success();
        }

        #endregion
    }
}
