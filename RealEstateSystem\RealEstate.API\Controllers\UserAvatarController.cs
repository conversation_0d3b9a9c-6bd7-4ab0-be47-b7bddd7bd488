using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.API.Controllers;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using System.Security.Claims;

namespace RealEstate.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class UserAvatarController : BaseController
    {
        private readonly IUserService _userService;
        private readonly IUserAvatarService _userAvatarService;
        private readonly IImageProcessingService _imageProcessingService;

        public UserAvatarController(
            IUserService userService,
            IUserAvatarService userAvatarService,
            IImageProcessingService imageProcessingService)
        {
            _userService = userService;
            _userAvatarService = userAvatarService;
            _imageProcessingService = imageProcessingService;
        }

        [HttpPost("upload")]
        [Authorize(Policy = "UserExists")]
        public async Task<ActionResult<UserDto>> UploadAvatar(IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest(new { Message = "No file uploaded." });
            }

            var userId = GetUserId();
            if (userId == null)
            {
                return BadRequest(new { Message = "Invalid user" });
            }

            // Skip non-image files
            if (!file.ContentType.StartsWith("image/"))
            {
                return BadRequest(new { Message = "Only image files are allowed." });
            }

            // Generate a unique file ID (UUID-based)
            var fileId = Guid.NewGuid();
            var extension = Path.GetExtension(file.FileName);

            // Create user-specific folder for avatars
            string userFolder = Path.Combine("UserAvatars", userId.Value.ToString());
            if (!Directory.Exists(userFolder))
            {
                Directory.CreateDirectory(userFolder);
            }

            // Define the direct file path
            var filePath = Path.Combine(userFolder, $"{fileId}{extension}");

            // Save the file directly to the user's folder
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            // Generate a public URL
            string publicUrl = $"{Request.Scheme}://{Request.Host}/useravatarmedia/{fileId}";

            // Store metadata in the database
            var userAvatar = new CreateUserAvatarDto
            {
                Id = fileId,
                UserID = userId.Value,
                MediaType = file.ContentType,
                MediaURL = publicUrl,
                FilePath = filePath,
                UploadedAt = DateTime.UtcNow
            };

            await _userAvatarService.CreateUserAvatarAsync(userAvatar);

            // Get updated user data
            var updatedUser = await _userService.GetUserByIdAsync(userId.Value);

            return Ok(updatedUser);
        }

        [HttpGet]
        [Authorize(Policy = "UserExists")]
        public async Task<ActionResult<UserAvatarDto>> GetAvatarImage()
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return BadRequest(new { Message = "Invalid user" });
            }

            var userAvatar = await _userAvatarService.GetUserAvatarByUserIdAsync(userId.Value);
            if (userAvatar == null)
            {
                return NotFound(new { Message = "No avatar image found for this user." });
            }

            return Ok(userAvatar);
        }

        [HttpDelete]
        [Authorize(Policy = "UserExists")]
        public async Task<ActionResult> RemoveAvatar()
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return BadRequest(new { Message = "Invalid user" });
            }

            // Get the user's avatar
            var userAvatar = await _userAvatarService.GetUserAvatarByUserIdAsync(userId.Value);
            if (userAvatar != null)
            {
                // Delete the avatar
                await _userAvatarService.DeleteUserAvatarAsync(userAvatar.Id);
            }

            return NoContent();
        }
    }
}
