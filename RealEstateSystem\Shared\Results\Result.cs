﻿using Shared.Enums;

namespace Shared.Results
{
    public class Result
    {
        public bool IsSuccess { get; }
        public string? ErrorMessage { get; }
        public ErrorType? ErrorType { get; }

        public bool IsFailure => !IsSuccess;

        protected Result(bool isSuccess, string? errorMessage, ErrorType? errorType = null) // Thêm tham số
        {
            IsSuccess = isSuccess;
            ErrorMessage = errorMessage;
            ErrorType = errorType; 
        }

        // Cập nhật hàm Failure để nhận thêm ErrorType
        public static Result Failure(string errorMessage, ErrorType errorType)
            => new Result(false, errorMessage, errorType);

        public static Result Success() => new Result(true, null);
    }

    public class Result<T> : Result
    {
        public T? Value { get; }

        protected Result(bool isSuccess, T? value, string? errorMessage, ErrorType? errorType = null)
            : base(isSuccess, errorMessage, errorType)
        {
            Value = value;
        }

        public static new Result<T> Failure(string errorMessage, ErrorType errorType)
            => new Result<T>(false, default, errorMessage, errorType);

        public static Result<T> Success(T value) => new Result<T>(true, value, null, null);
    }
}
