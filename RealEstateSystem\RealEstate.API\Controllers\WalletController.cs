﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Wallet;
using RealEstate.Application.Interfaces;
using System.Text.Json;

namespace RealEstate.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class WalletController : BaseController
    {
        private readonly IWalletService _walletService;
        private readonly ILogger<WalletController> _logger;

        public WalletController(
            IWalletService walletService,
            ILogger<WalletController> logger)
        {
            _walletService = walletService;
            _logger = logger;
        }

        [HttpPost("topup")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> TopUpWallet([FromBody] TopUpWalletDto request)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("Invalid user");

            var result = await _walletService.TopUpAsync(userId.Value, request);
            return HandleResult(result);
        }

        [HttpPost("spend")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> SpendFromWallet([FromBody] SpendWalletDto request)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("Invalid user");

            var result = await _walletService.SpendAsync(userId.Value, request);
            return HandleResult(result);
        }

        [HttpPost("payment-notification")]
        [AllowAnonymous] // BẮT BUỘC: Cho phép truy cập công khai mà không cần token xác thực
        public async Task<IActionResult> HandlePaymentNotification([FromBody] PaymentNotificationDto request)
        {
            _logger.LogInformation("Received payment notification: {Notification}", JsonSerializer.Serialize(request));

            // Gọi đến service để xử lý nghiệp vụ
            var result = await _walletService.HandlePaymentNotificationAsync(request);

            // Nếu xử lý thành công, trả về HTTP 200 OK cho cổng thanh toán
            // để họ biết bạn đã nhận được và không gửi lại nữa.
            if (result.IsSuccess)
            {
                return Ok(new { message = "Notification processed successfully." });
            }

            // Nếu xử lý thất bại (ví dụ: chữ ký không hợp lệ, giao dịch không tìm thấy),
            // trả về HTTP 400 Bad Request để cổng thanh toán không cố gắng gửi lại một request lỗi.
            return BadRequest(new { message = "Failed to process notification.", errors = result.ErrorMessage });
        }

        [HttpGet("balance")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> GetBalance()
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("Invalid user");

            var result = await _walletService.GetBalanceAsync(userId.Value);
            return HandleResult(result);
        }

        [HttpGet]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> GetTransactions(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("Invalid user");

            var result = await _walletService.GetTransactionsAsync(userId.Value, page, pageSize);
            return HandleResult(result);
        }

        [HttpGet("{id}")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> GetTransactionById(Guid id)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("Invalid user");

            var result = await _walletService.GetTransactionByIdAsync(id, userId.Value);
            return HandleResult(result);
        }

        [HttpGet("status/{refId}")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> GetTransactionStatusByRefId(string refId)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("Invalid user");

            var result = await _walletService.GetTransactionByRefIdAsync(refId, userId.Value);
            return HandleResult(result);
        }

        [HttpGet("pending")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> GetUserPendingTransactions(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("Invalid user");
            
            var result = await _walletService.GetUserPendingTransactionsAsync(userId.Value, page, pageSize);
            return HandleResult(result);
        }

        [HttpGet("search")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> SearchTransactions(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string? status = null,
            [FromQuery] string? paymentMethod = null,
            [FromQuery] string? type = null,
            [FromQuery] decimal? minAmount = null,
            [FromQuery] decimal? maxAmount = null,
            [FromQuery] string? searchTerm = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("Invalid user");
            
            var criteria = new TransactionSearchCriteriaDto
            {
                StartDate = startDate,
                EndDate = endDate,
                Status = status,
                PaymentMethod = paymentMethod,
                Type = type,
                MinAmount = minAmount,
                MaxAmount = maxAmount,
                SearchTerm = searchTerm,
                Page = page,
                PageSize = pageSize
            };

            var result = await _walletService.SearchTransactionsAsync(criteria, userId.Value);
            return HandleResult(result);
        }

        [HttpGet("export")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> ExportTransactions(
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string? status = null,
            [FromQuery] string? paymentMethod = null,
            [FromQuery] string? type = null,
            [FromQuery] decimal? minAmount = null,
            [FromQuery] decimal? maxAmount = null,
            [FromQuery] string? searchTerm = null)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("Invalid user");

            var criteria = new TransactionSearchCriteriaDto
            {
                StartDate = startDate,
                EndDate = endDate,
                Status = status,
                PaymentMethod = paymentMethod,
                Type = type,
                MinAmount = minAmount,
                MaxAmount = maxAmount,
                SearchTerm = searchTerm
            };

            var result = await _walletService.ExportTransactionsToExcelAsync(criteria, userId.Value);

            if (!result.IsSuccess)
            {
                return HandleResult(result);
            }

            string fileName = $"Transactions_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
            return File(result.Value, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
        }
    }
}
