version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: realestate-postgres
    environment:
      POSTGRES_DB: realestate_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: your_password_here
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - realestate-network

  # RealEstate.API
  realestate-api:
    build:
      context: .
      dockerfile: RealEstate.API/Dockerfile
    container_name: realestate-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=realestate_db;Username=postgres;Password=your_password_here
      # JWT Configuration
      - JWT__Key=your_jwt_secret_key_here_minimum_32_characters
      - JWT__Issuer=RealEstateAPI
      - JWT__Audience=RealEstateUsers
      # CORS Configuration
      - Cors__AllowedOrigins__0=http://localhost:3000
      - Cors__AllowedOrigins__1=https://yourdomain.com
      # Storage Configuration (Local or S3)
      - Storage__Provider=Local
      # AWS Configuration (if using S3)
      # - AWS__S3__AccessKey=your_s3_access_key
      # - AWS__S3__SecretKey=your_s3_secret_key
      # - AWS__S3__Region=us-east-1
      # - AWS__S3__BucketName=your-bucket-name
      # AWS SES Configuration
      # - AWS__SES__AccessKey=your_ses_access_key
      # - AWS__SES__SecretKey=your_ses_secret_key
      # - AWS__SES__Region=us-east-1
      # Internal Email Configuration
      - InternalEmail__NoReply=<EMAIL>
      - InternalEmail__Support=<EMAIL>
      - InternalEmail__ReivewPost=<EMAIL>
    ports:
      - "5049:8080"
    depends_on:
      - postgres
    volumes:
      - api_property_images:/app/PropertyImages
      - api_user_avatars:/app/UserAvatars
      - api_temp:/app/Temp
    networks:
      - realestate-network

  # RealEstate.InternalAPI
  realestate-internal-api:
    build:
      context: .
      dockerfile: RealEstate.InternalAPI/Dockerfile
    container_name: realestate-internal-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__YezHomeConnection=Host=postgres;Database=realestate_db;Username=postgres;Password=your_password_here
      # JWT Configuration
      - JWT__Key=your_jwt_secret_key_here_minimum_32_characters
      - JWT__Issuer=RealEstateInternalAPI
      - JWT__Audience=RealEstateAdmins
      # CORS Configuration
      - Cors__AllowedOrigins__0=http://localhost:3001
      - Cors__AllowedOrigins__1=https://admin.yourdomain.com
      # AWS Configuration (if needed)
      # - AWS__SES__AccessKey=your_ses_access_key
      # - AWS__SES__SecretKey=your_ses_secret_key
      # - AWS__SES__Region=us-east-1
    ports:
      - "5176:8080"
    depends_on:
      - postgres
    networks:
      - realestate-network

volumes:
  postgres_data:
  api_property_images:
  api_user_avatars:
  api_temp:

networks:
  realestate-network:
    driver: bridge
