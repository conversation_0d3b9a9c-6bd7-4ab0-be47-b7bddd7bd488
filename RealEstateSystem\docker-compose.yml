version: '3.8'

services:
  # PostgreSQL Database
  yezhome_postgres:
    image: postgres:17
    container_name: yezhome-postgres
    environment:
      POSTGRES_DB: yezhome_db
      POSTGRES_USER: yezhome_postgres_ad
      POSTGRES_PASSWORD: pL30/T1x(CYg
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - yezhome-network

  # RealEstate.API
  yezhome-api:
    build:
      context: .
      dockerfile: RealEstate.API/Dockerfile
    container_name: yezhome-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Host=yezhome_postgres;Database=yezhome_db;Username=yezhome_postgres_ad;Password=pL30/T1x(CYg
      # JWT Configuration
      - JWT__Key=4b20fzh57wPFxX6E3FaLeXfLbZcKDpz5fmsonewQc9IsJR2Ix7
      - JWT__Issuer=yezhomeAPI
      - JWT__Audience=yezhomeUsers
      # CORS Configuration
      - Cors__AllowedOrigins__0=http://localhost:3000
      - Cors__AllowedOrigins__1=https://yezhome.vn
      # Storage Configuration (Local or S3)
      - Storage__Provider=Local
      # AWS Configuration (if using S3)
      - AWS__S3__AccessKey=your_s3_access_key
      - AWS__S3__SecretKey=your_s3_secret_key
      - AWS__S3__Region=us-east-1
      - AWS__S3__BucketName=your-bucket-name
      # AWS SES Configuration
      - AWS__SES__AccessKey=********************
      - AWS__SES__SecretKey=XkcMfAmRCORAtbfd7Hgr9fR3FY7ca8b/9iS+HCgk
      - AWS__SES__Region=ap-southeast-1
      # Internal Email Configuration
      - InternalEmail__NoReply=<EMAIL>
      - InternalEmail__Support=<EMAIL>
      - InternalEmail__ReivewPost=<EMAIL>
    ports:
      - "5049:8080"
    depends_on:
      - postgres
    volumes:
      - api_property_images:/app/PropertyImages
      - api_user_avatars:/app/UserAvatars
      - api_temp:/app/Temp
    networks:
      - yezhome-network

  # RealEstate.InternalAPI
  yezhome-internal-api:
    build:
      context: .
      dockerfile: RealEstate.InternalAPI/Dockerfile
    container_name: yezhome-internal-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__YezHomeConnection=Host=postgres;Database=yezhome_db;Username=postgres;Password=your_password_here
      # JWT Configuration
      - JWT__Key=your_jwt_secret_key_here_minimum_32_characters
      - JWT__Issuer=yezhomeInternalAPI
      - JWT__Audience=yezhomeAdmins
      # CORS Configuration
      - Cors__AllowedOrigins__0=http://localhost:3001
      - Cors__AllowedOrigins__1=https://admin.yourdomain.com
      # AWS Configuration (if needed)
      # - AWS__SES__AccessKey=your_ses_access_key
      # - AWS__SES__SecretKey=your_ses_secret_key
      # - AWS__SES__Region=us-east-1
    ports:
      - "5176:8080"
    depends_on:
      - postgres
    networks:
      - yezhome-network

volumes:
  postgres_data:
  api_property_images:
  api_user_avatars:
  api_temp:

networks:
  yezhome-network:
    driver: bridge
