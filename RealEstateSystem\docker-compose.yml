version: '3.8'

services:
  # PostgreSQL Database
  yezhome_postgres:
    image: postgres:17
    container_name: yezhome-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - yezhome-network

  # RealEstate.API
  yezhome-api:
    build:
      context: .
      dockerfile: RealEstate.API/Dockerfile
    container_name: yezhome-api
    environment:
      - ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT}
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Host=yezhome_postgres;Database=${POSTGRES_DB};Username=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      # JWT Configuration
      - JWT__Key=${JWT_KEY}
      - JWT__Issuer=${JWT_ISSUER}
      - JWT__Audience=${JWT_AUDIENCE}
      # CORS Configuration
      - Cors__AllowedOrigins__0=http://localhost:3000
      - Cors__AllowedOrigins__1=https://yezhome.vn
      # Storage Configuration
      - Storage__Provider=${STORAGE_PROVIDER}
      # AWS S3 Configuration
      - AWS__S3__AccessKey=${AWS_S3_ACCESS_KEY}
      - AWS__S3__SecretKey=${AWS_S3_SECRET_KEY}
      - AWS__S3__Region=${AWS_S3_REGION}
      - AWS__S3__BucketName=${AWS_S3_BUCKET_NAME}
      # AWS SES Configuration
      - AWS__SES__AccessKey=${AWS_SES_ACCESS_KEY}
      - AWS__SES__SecretKey=${AWS_SES_SECRET_KEY}
      - AWS__SES__Region=${AWS_SES_REGION}
      - AWS__SES__FromEmail=${AWS_SES_FROM_EMAIL}
      - AWS__SES__FromName=${AWS_SES_FROM_NAME}
      # Internal Email Configuration
      - InternalEmail__NoReply=${INTERNAL_EMAIL_NOREPLY}
      - InternalEmail__Support=${INTERNAL_EMAIL_SUPPORT}
      - InternalEmail__ReivewPost=${INTERNAL_EMAIL_REVIEW}
    ports:
      - "5049:8080"
    depends_on:
      - yezhome_postgres
    volumes:
      - api_property_images:/app/PropertyImages
      - api_user_avatars:/app/UserAvatars
      - api_temp:/app/Temp
    networks:
      - yezhome-network

  # RealEstate.InternalAPI
  yezhome-internal-api:
    build:
      context: .
      dockerfile: RealEstate.InternalAPI/Dockerfile
    container_name: yezhome-internal-api
    environment:
      - ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT}
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__YezHomeConnection=Host=yezhome_postgres;Database=${POSTGRES_DB};Username=${POSTGRES_USER};Password=${POSTGRES_PASSWORD}
      # JWT Configuration
      - JWT__Key=${INTERNAL_JWT_KEY}
      - JWT__Issuer=${INTERNAL_JWT_ISSUER}
      - JWT__Audience=${INTERNAL_JWT_AUDIENCE}
      # CORS Configuration
      - Cors__AllowedOrigins__0=http://localhost:3001
      - Cors__AllowedOrigins__1=https://admin.yezhome.vn
      # AWS SES Configuration (if needed)
      - AWS__SES__AccessKey=${AWS_SES_ACCESS_KEY}
      - AWS__SES__SecretKey=${AWS_SES_SECRET_KEY}
      - AWS__SES__Region=${AWS_SES_REGION}
    ports:
      - "5176:8080"
    depends_on:
      - yezhome_postgres
    networks:
      - yezhome-network

volumes:
  postgres_data:
  api_property_images:
  api_user_avatars:
  api_temp:

networks:
  yezhome-network:
    driver: bridge
