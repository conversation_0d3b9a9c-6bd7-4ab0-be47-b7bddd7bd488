﻿using AutoMapper;
using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Notification;
using RealEstate.Application.DTO.UserFavorite;
using RealEstate.Domain.Entities;

namespace RealEstate.Application.AutoMapperProfiles
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // AdminRole
            CreateMap<AdminRole, AdminRoleDto>().ReverseMap();
            CreateMap<AdminRole, CreateAdminRoleDto>();

            // User
            CreateMap<AppUser, UserDto>()
                .ForMember(dest => dest.AvatarImage, opt => opt.MapFrom(src => src.AvatarImage))
                .ForMember(dest => dest.PersonalTaxCode, opt => opt.MapFrom(src => src.PersonalTaxCode))
                .ForMember(dest => dest.InvoiceInfo, opt => opt.Ignore()) // Handled manually in service
                .ReverseMap();
            CreateMap<CreateUserDto, AppUser>()
                .ForMember(dest => dest.PasswordHash, opt => opt.Ignore())
                .ForMember(dest => dest.PasswordSalt, opt => opt.Ignore());

            // User Invoice Info
            CreateMap<UserInvoiceInfoDto, AppUser>()
                .ForMember(dest => dest.InvoiceBuyerName, opt => opt.MapFrom(src => src.BuyerName))
                .ForMember(dest => dest.InvoiceEmail, opt => opt.MapFrom(src => src.Email))
                .ForMember(dest => dest.InvoiceCompanyName, opt => opt.MapFrom(src => src.CompanyName))
                .ForMember(dest => dest.InvoiceTaxCode, opt => opt.MapFrom(src => src.TaxCode))
                .ForMember(dest => dest.InvoiceAddress, opt => opt.MapFrom(src => src.Address));

            CreateMap<AppUser, UserInvoiceInfoDto>()
                .ForMember(dest => dest.BuyerName, opt => opt.MapFrom(src => src.InvoiceBuyerName))
                .ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.InvoiceEmail))
                .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(src => src.InvoiceCompanyName))
                .ForMember(dest => dest.TaxCode, opt => opt.MapFrom(src => src.InvoiceTaxCode))
                .ForMember(dest => dest.Address, opt => opt.MapFrom(src => src.InvoiceAddress));

            // User Invoice information for Acountant
            CreateMap<AppUser, UserTaxInfoDto>()
            .ForMember(dest => dest.PersonalTaxCode, opt => opt.MapFrom(src => src.PersonalTaxCode))
            .ForMember(dest => dest.InvoiceInfo, opt => opt.MapFrom(src =>
                 new UserInvoiceInfoDto
                 {
                     BuyerName = src.InvoiceBuyerName ?? string.Empty,
                     Email = src.InvoiceEmail ?? string.Empty,
                     CompanyName = src.InvoiceCompanyName ?? string.Empty,
                     TaxCode = src.InvoiceTaxCode ?? string.Empty,
                     Address = src.InvoiceAddress ?? string.Empty
                 }));

            CreateMap<AddUserRoleDto, UserRole>()
                .ForMember(dest => dest.UserID, opt => opt.MapFrom(src => src.UserId))
                .ForMember(dest => dest.RoleID, opt => opt.MapFrom(src => src.RoleId));

            CreateMap<UpdateUserAvatarDto, AppUser>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.UserId))
                .ForMember(dest => dest.AvatarImage, opt => opt.MapFrom(src => src.AvatarImage));

            // Property
            CreateMap<Property, PropertyDto>().ReverseMap();
            CreateMap<CreatePropertyDto, Property>();

            // Property Media
            CreateMap<PropertyMedia, PropertyMediaDto>().ReverseMap();
            CreateMap<PropertyMedia, PropertyMediaDtoResponse>().ReverseMap();
            CreateMap<AppUser, UserInformationDto>().ReverseMap();
            CreateMap<CreateMediaDto, PropertyMedia>();

            // User Avatar
            CreateMap<UserAvatar, UserAvatarDto>().ReverseMap();
            CreateMap<CreateUserAvatarDto, UserAvatar>();

            // Blog Post
            CreateMap<BlogPost, BlogPostDto>()
                .ForMember(dest => dest.AuthorName, opt => opt.MapFrom(src => src.Author.FullName))
                .ForMember(dest => dest.BlogComments, opt => opt.MapFrom(src => src.BlogComments))
                .ReverseMap();
            CreateMap<CreateBlogPostDto, BlogPost>();

            // Blog Comment
            CreateMap<BlogComment, BlogCommentDto>().ReverseMap();
            CreateMap<CreateBlogCommentDto, BlogComment>();

            // Property Status Log
            CreateMap<PropertyStatusLog, PropertyStatusLogDto>().ReverseMap();

            // User Favorite
            CreateMap<UserFavorite, UserFavoriteDto>().ReverseMap();
            CreateMap<CreateUserFavoriteDto, UserFavorite>()
                .ForMember(dest => dest.UserID, opt => opt.Ignore())  // UserID is set from context
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(_ => DateTime.UtcNow));

            // City
            CreateMap<City, CityDto>().ReverseMap();

            // District
            CreateMap<District, DistrictDto>().ReverseMap();

            // Ward
            CreateMap<Ward, WardDto>().ReverseMap();

            // Street
            CreateMap<Street, StreetDto>().ReverseMap();

            // Project
            CreateMap<Project, ProjectDto>().ReverseMap();

            // ContactRequest
            // Mapping từ Entity -> DTO
            CreateMap<ContactRequest, ContactRequestDto>();

            // Mapping từ DTO -> Entity
            CreateMap<CreateContactRequestDto, ContactRequest>();

            // Mapping từ Update DTO -> Entity
            CreateMap<UpdateContactRequestDto, ContactRequest>()
                .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => DateTime.UtcNow));

            // wallet
            CreateMap<Wallet, WalletDto>().ReverseMap();

            // wallet transaction
            CreateMap<WalletTransaction, WalletTransactionDto>().ReverseMap();

            // member ranking
            CreateMap<MemberRanking, MemberRankingDto>().ReverseMap();

            // highlight fee
            CreateMap<HighlightFee, HighlightFeeDto>().ReverseMap();

            // Mapping for Notification -> NotificationDto
            CreateMap<Notification, NotificationDto>().ReverseMap();

            // Mapping for NotificationPreference -> NotificationPreferenceDto
            CreateMap<NotificationPreference, NotificationPreferenceDto>().ReverseMap();

            // Mapping for WalletTransaction -> WalletTransactionDto
            CreateMap<WalletTransaction, WalletTransactionDto>();

            CreateMap<Notification, NotificationDto>();
            CreateMap<CreateNotificationDto, Notification>();

            // Permission System
            CreateMap<Permission, PermissionDto>().ReverseMap();
            CreateMap<CreatePermissionDto, Permission>();

            CreateMap<RolePermission, RolePermissionDto>().ReverseMap();
            CreateMap<CreateRolePermissionDto, RolePermission>();

            // Setting
            CreateMap<Setting, SettingDto>().ReverseMap();
            CreateMap<SettingCreateRequest, Setting>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.Type, opt => opt.MapFrom(src => "string"))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description));
            CreateMap<SettingUpdateItem, Setting>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.Type, opt => opt.Ignore())
                .ForMember(dest => dest.Description, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore());

        }
    }
}
