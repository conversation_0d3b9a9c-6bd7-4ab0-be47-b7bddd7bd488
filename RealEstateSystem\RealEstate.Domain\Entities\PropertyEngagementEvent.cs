using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RealEstate.Domain.Entities
{
    public class PropertyEngagementEvent : BaseEntity
    {
        [Required]
        public Guid PropertyId { get; set; }
        
        public Guid? UserId { get; set; }
        
        [Required]
        [StringLength(50)]
        public string EventType { get; set; } = string.Empty; // view, favorite, click_phone, chat, etc.
        
        [StringLength(255)]
        public string? SessionId { get; set; }

        [StringLength(255)]
        public string? DeviceId { get; set; }
        
        public string? UserAgent { get; set; }
        
        [StringLength(50)]
        public string? IpAddress { get; set; }
        
        [StringLength(20)]
        public string? DeviceType { get; set; } // mobile, desktop, tablet
        
        [StringLength(50)]
        public string? Platform { get; set; } // iOS, Android, Windows...
        
        [StringLength(50)]
        public string? Browser { get; set; } // Chrome, Safari, etc.
        
        [StringLength(100)]
        public string? City { get; set; }
        
        [StringLength(100)]
        public string? Region { get; set; }
        
        [StringLength(100)]
        public string? Country { get; set; }
        
        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        [ForeignKey("PropertyId")]
        public Property? Property { get; set; }
        
        [ForeignKey("UserId")]
        public AppUser? User { get; set; }
    }
} 