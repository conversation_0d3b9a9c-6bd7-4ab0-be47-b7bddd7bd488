﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RealEstate.Domain.Entities
{
    public class HighlightFee : BaseEntity
    {
        [Key]
        [ForeignKey("MemberRanking")]
        public string RankName { get; set; }

        [Column(TypeName = "numeric(20,2)")]
        public decimal Fee { get; set; }

        public virtual MemberRanking MemberRanking { get; set; }
    }
}
