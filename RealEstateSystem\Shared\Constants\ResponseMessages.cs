﻿namespace Shared.Constants
{
    public static class ResponseMessages
    {
        // Thông báo thành công
        public const string OperationSuccess = "Thao tác thành công";
        public const string CreateSuccess = "Tạo mới thành công";
        public const string UpdateSuccess = "Cập nhật thành công";

        // Thông báo lỗi
        public const string NotFound = "Đối tượng không tồn tại";
        public const string ValidationError = "Dữ liệu không hợp lệ";
        public const string Unauthorized = "Bạn không có quyền truy cập";
        public const string InternalServerError = "Đã có lỗi xảy ra ở hệ thống. Vui lòng thử lại sau";
        public const string TokenExpiredError = "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại";
        public const string ResponseAlreadyStarted = "Response has already started, the error handler will not execute on HTTP";        
    }

    public static class AuthenticateMessage
    {
        public const string UsernamePasswordNotValid = "Thông tin đăng nhập không đúng";
        public const string AccountUnabled = "Tài khoản của bạn đã bị vô hiệu hóa. Vui lòng liên hệ quản trị viên để kích hoạt lại";
        public const string OldPasswordNotCorrect = "Mật khẩu cũ không đúng";
    }

    public static class BlogPostMessage
    {
        public const string BlogPostNotFound = "Không tìm thấy bài viết";
    }

    public static class ContactRequestMessage
    {
        public const string ContactRequestNotFound = "Không tìm thấy yêu cầu liên hệ";
        public const string CreateContactRequestError = "Đã xảy ra lỗi khi tạo yêu cầu liên hệ. Vui lòng thử lại sau";
        public const string UpdateContactRequestError = "Đã xảy ra lỗi khi cập nhật yêu cầu liên hệ. Vui lòng thử lại sau";
        public const string DeleteContactRequestError = "Đã xảy ra lỗi khi xóa yêu cầu liên hệ. Vui lòng thử lại sau";
        public const string InvalidContactRequestData = "Dữ liệu yêu cầu liên hệ không hợp lệ";
    }

    public static class RolePermissionMessage
    {
        public const string RoleNotFound = "Không tìm thấy vai trò";
        public const string PermissionNotFound = "Không tìm thấy quyền";
        public const string RoleCodeAlreadyExists = "Mã vai trò đã tồn tại";
        public const string CannotDeleteRoleWithAssignedUsers = "Không thể xóa vai trò có người dùng được gán";
        public const string RolePermissionAssignmentFailed = "Không thể gán quyền cho vai trò";
        public const string RolePermissionRemovalFailed = "Không thể xóa quyền khỏi vai trò";
    }

    public static class SettingMessage
    {
        public const string SettingNotFound = "Không tìm thấy cài đặt";
        public const string SettingKeyAlreadyExists = "Khóa cài đặt đã tồn tại";
        public const string InvalidSettingData = "Dữ liệu cài đặt không hợp lệ";
        public const string CreateSettingError = "Đã xảy ra lỗi khi tạo cài đặt. Vui lòng thử lại sau";
        public const string UpdateSettingError = "Đã xảy ra lỗi khi cập nhật cài đặt. Vui lòng thử lại sau";
    }
}
