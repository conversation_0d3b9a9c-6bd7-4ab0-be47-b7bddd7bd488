﻿using RealEstate.Application.DTO.Notification;
using RealEstate.Application.Interfaces;
using Shared.Enums;
using Shared.Results;

namespace RealEstate.Infrastructure.Services.Notifications
{
    public class CompositeNotificationService : INotificationService
    {
        private readonly InAppNotificationService _inAppService;
        private readonly PushNotificationService _pushService;
        private readonly EmailNotificationService _emailService;
        public CompositeNotificationService(
            InAppNotificationService inAppService,
            PushNotificationService pushService,
            EmailNotificationService emailService)
        {
            _inAppService = inAppService;
            _pushService = pushService;
            _emailService = emailService;
        }
        public async Task<Result> SendAsync(NotificationRequest request)
        {
            var tasks = new List<Task>();

            // Kiểm tra cờ (flag) để quyết định gọi service nào
            if (request.TargetChannels.HasFlag(NotificationChannel.InApp))
            {
                tasks.Add(_inAppService.SendAsync(request));
            }

            if (request.TargetChannels.HasFlag(NotificationChannel.Push))
            {
                tasks.Add(_pushService.SendAsync(request));
            }

            if (request.TargetChannels.HasFlag(NotificationChannel.Email))
            {
                tasks.Add(_emailService.SendAsync(request));
            }

            // Chạy song song các tác vụ gửi và chờ tất cả hoàn thành
            await Task.WhenAll(tasks);

            // Xử lý kết quả trả về nếu cần
            return Result.Success();
        }
    }
}
