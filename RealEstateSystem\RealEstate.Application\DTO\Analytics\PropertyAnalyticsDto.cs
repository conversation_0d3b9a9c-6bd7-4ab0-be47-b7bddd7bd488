using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.Application.DTO.Analytics
{
    public class PropertyAnalyticsDto
    {
        public Guid PropertyId { get; set; }
        public string? PropertyTitle { get; set; }
        public PropertyStatus PropertyStatus { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? ExpiresAt { get; set; }
        
        // Engagement metrics
        public int TotalViews { get; set; }
        public int TotalFavorites { get; set; }
        
        // Financial metrics
        public decimal TotalSpent { get; set; }
        public decimal ExtensionSpent { get; set; }
        public decimal HighlightSpent { get; set; }
        
        // Trend data
        public List<DailyViewsDto> ViewsTrend { get; set; } = new List<DailyViewsDto>();
        public List<DailySpendingDto> SpendingTrend { get; set; } = new List<DailySpendingDto>();
    }
}
