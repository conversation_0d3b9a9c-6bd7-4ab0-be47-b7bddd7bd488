﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.Interfaces;
using Shared.Responses;
using System.Security.Claims;

namespace RealEstate.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class SettingController : BaseController
    {
        private readonly ISettingService _settingService;

        public SettingController(ISettingService settingService) 
        {
            _settingService = settingService;
        }

        [HttpGet]
        public async Task<IActionResult> GetSettings()
        {
            var result = await _settingService.GetAllAsync();
            return HandleResult(result);
        }
    }
}
