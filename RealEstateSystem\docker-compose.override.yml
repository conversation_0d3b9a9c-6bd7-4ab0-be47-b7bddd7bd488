version: '3.8'

services:
  # Development overrides for RealEstate.API
  realestate-api:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      # Development database connection
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=realestate_dev_db;Username=postgres;Password=dev_password
      # Development JWT settings (use a simple key for development)
      - JWT__Key=development_jwt_secret_key_32_chars_min
      - JWT__Issuer=RealEstateAPI-Dev
      - JWT__Audience=RealEstateUsers-Dev
      # Development CORS - allow all origins
      - Cors__AllowedOrigins__0=http://localhost:3000
      - Cors__AllowedOrigins__1=http://localhost:3001
      - Cors__AllowedOrigins__2=http://127.0.0.1:3000
      - Cors__AllowedOrigins__3=http://127.0.0.1:3001
      # Use local storage for development
      - Storage__Provider=Local
      # Development email settings (use fake/test values)
      - InternalEmail__NoReply=noreply@localhost
      - InternalEmail__Support=support@localhost
      - InternalEmail__ReivewPost=review@localhost
    volumes:
      # Mount local directories for development
      - ./RealEstate.API/PropertyImages:/app/PropertyImages
      - ./RealEstate.API/UserAvatars:/app/UserAvatars
      - ./RealEstate.API/Temp:/app/Temp
      - ./RealEstate.API/Templates:/app/Templates

  # Development overrides for RealEstate.InternalAPI
  realestate-internal-api:
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      # Development database connection
      - ConnectionStrings__YezHomeConnection=Host=postgres;Database=realestate_dev_db;Username=postgres;Password=dev_password
      # Development JWT settings
      - JWT__Key=development_jwt_secret_key_32_chars_min
      - JWT__Issuer=RealEstateInternalAPI-Dev
      - JWT__Audience=RealEstateAdmins-Dev
      # Development CORS - allow all origins
      - Cors__AllowedOrigins__0=http://localhost:3000
      - Cors__AllowedOrigins__1=http://localhost:3001
      - Cors__AllowedOrigins__2=http://127.0.0.1:3000
      - Cors__AllowedOrigins__3=http://127.0.0.1:3001

  # Development database settings
  postgres:
    environment:
      POSTGRES_DB: realestate_dev_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5433:5432"  # Use different port for development to avoid conflicts
