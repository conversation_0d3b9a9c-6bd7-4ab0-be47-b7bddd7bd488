﻿namespace RealEstate.API.Controllers
{
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Http;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Logging;
    using RealEstate.Application.DTO;
    using RealEstate.Application.DTO.Notification;
    using RealEstate.Application.Interfaces;
    using RealEstate.Domain.Common;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    [Route("api/notifications")]
    [ApiController]
    [Authorize]
    public class NotificationController : BaseController
    {
        private readonly INotificationHistoryService _notificationHistoryService;

        public NotificationController(INotificationHistoryService notificationHistoryService)
        {
            _notificationHistoryService = notificationHistoryService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return Unauthorized("Invalid user");
            }

            var result = await _notificationHistoryService.GetNotificationsForUserAsync(
                userId.Value,
                null,
                null,
                page,
                pageSize);

            return HandleResult(result);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            var result = await _notificationHistoryService.GetNotificationByIdAsync(id);
            return HandleResult(result);
        }

        [HttpGet("category/{category}")]
        public async Task<IActionResult> GetByCategory(
            string category,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] bool unreadOnly = false)
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return Unauthorized("Invalid user");
            }

            if (!FunctionHelper.IsValidNotificationCategory(category))
            {
                return BadRequest("Invalid notification category");
            }

            var result = await _notificationHistoryService.GetNotificationsForUserAsync(
                userId.Value,
                category,
                unreadOnly,
                fromDate,
                toDate,
                page,
                pageSize);

            return HandleResult(result);
        }

        [HttpPut("{id}/mark-as-read")]
        public async Task<IActionResult> MarkAsRead(Guid id)
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return Unauthorized("Invalid user");
            }

            var result = await _notificationHistoryService.MarkAsReadAsync(id, userId.Value);
            return HandleResult(result);
        }

        [HttpPut("mark-all-as-read")]
        public async Task<IActionResult> MarkAllAsRead()
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return Unauthorized("Invalid user");
            }

            var result = await _notificationHistoryService.MarkAllAsReadAsync(userId.Value);
            return HandleResult(result);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(Guid id)
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return Unauthorized("Invalid user");
            }

            var result = await _notificationHistoryService.DeleteNotificationAsync(id, userId.Value);
            return HandleResult(result);
        }

        [HttpGet("unread-count")]
        public async Task<IActionResult> GetUnreadCount()
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return Unauthorized("Invalid user");
            }

            var result = await _notificationHistoryService.GetUnreadNotificationCountAsync(userId.Value);
            return HandleResult(result);
        }
    }
}
