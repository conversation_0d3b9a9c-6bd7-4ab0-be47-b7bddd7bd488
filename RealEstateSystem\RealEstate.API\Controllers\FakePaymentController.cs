﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO.Wallet;
using RealEstate.Application.Interfaces;

namespace RealEstate.API.Controllers
{
    public class TestProcessRequest
    {
        public string TransactionReference { get; set; }
        public bool IsSuccess { get; set; }
    }

    [ApiController]
    [Route("api/[controller]")]
    #if !DEBUG
    [NonAction] // Vô hiệu hóa controller này trong môi trường Production
    #endif
    public class FakePaymentController : BaseController
    {
        private readonly IWalletService _walletService; // <PERSON><PERSON><PERSON> sử bạn có thể truy cập service xử lý

        public FakePaymentController(IWalletService walletService)
        {
            _walletService = walletService;
        }

        [HttpPost("payment-notification")]
        [AllowAnonymous]
        public async Task<IActionResult> ProcessTestTransaction([FromBody] TestProcessRequest request)
        {
            // <PERSON><PERSON><PERSON> lập DTO nhận được từ Cổng thanh toán thật
            var notification = new PaymentNotificationDto
            {
                TransactionReference = request.TransactionReference,
                IsSuccess = request.IsSuccess,
                FailureReason = request.IsSuccess ? null : "Simulated failure by user.",
                Signature = "test-signature" // Chữ ký giả, bỏ qua bước xác thực trong service
            };

            // Gọi đến hàm xử lý webhook thật sự
            var result = await _walletService.HandlePaymentNotificationAsync(notification);

            return Ok();
        }
    }
}
