﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using RealEstate.Application.Common;
using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Notification;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Common;
using RealEstate.Domain.CustomModel;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using Shared.Constants;
using Shared.Enums;
using Shared.Helper;
using Shared.Results;

namespace RealEstate.Application.Services
{
    public class ContactRequestService : IContactRequestService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<ContactRequestService> _logger;
        private readonly IServiceScopeFactory _scopeFactory;

        public ContactRequestService(IUnitOfWork unitOfWork, IMapper mapper, 
            ILogger<ContactRequestService> logger, IServiceScopeFactory scopeFactory)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
            _scopeFactory = scopeFactory;
        }

        public async Task<Result<ContactRequestDto>> GetByIdAsync(Guid id)
        {
            var request = await _unitOfWork.ContactRequests.GetByIdAsync(id);
            if (request == null)
                return Result<ContactRequestDto>.Failure(ContactRequestMessage.ContactRequestNotFound, ErrorType.NotFound);
            return Result<ContactRequestDto>.Success(_mapper.Map<ContactRequestDto>(request));
        }

        public async Task<Result<IEnumerable<ContactRequestDto>>> GetAllAsync()
        {
            var requests = await _unitOfWork.ContactRequests.GetAllAsync();
            return Result<IEnumerable<ContactRequestDto>>.Success(_mapper.Map<IEnumerable<ContactRequestDto>>(requests));
        }

        public async Task<Result<PagedResult<ContactRequestDto>>> GetPagedAsync(PagingRequest paging, string? phone)
        {
            string? normalizedPhone = null;
            if (!string.IsNullOrEmpty(phone))
            {
                normalizedPhone = PhoneUtils.NormalizePhoneNumber(phone);
                if (normalizedPhone == null)
                {
                    return Result<PagedResult<ContactRequestDto>>.Failure("Số điện thoại không hợp lệ", ErrorType.Validation);
                }
            }

            var requests = await _unitOfWork.ContactRequests.GetPagedAsync(
                paging,
                r => string.IsNullOrEmpty(normalizedPhone) || (r.Phone != null && r.Phone.Contains(normalizedPhone))
            );

            var pagedResult = new PagedResult<ContactRequestDto>
            {
                Items = _mapper.Map<IEnumerable<ContactRequestDto>>(requests.Items),
                TotalCount = requests.TotalCount
            };
            return Result<PagedResult<ContactRequestDto>>.Success(pagedResult);
        }

        public async Task<Result<IEnumerable<ContactRequestDto>>> GetByPropertyIdAsync(Guid propertyId)
        {
            var requests = await _unitOfWork.ContactRequests.FindAsync(r => r.PropertyId == propertyId, orderBy: query => query.OrderByDescending(p => p.CreatedAt));
            return Result<IEnumerable<ContactRequestDto>>.Success(_mapper.Map<IEnumerable<ContactRequestDto>>(requests));
        }

        public async Task<IEnumerable<ContactRequestDto>> CountActionRequestByPropertyIdAsync(Guid propertyId)
        {
            var requests = await _unitOfWork.ContactRequests.FindAsync(r => r.PropertyId == propertyId && r.Status == EnumValues.ContactRequestStatus.Pending.ToString());

            return _mapper.Map<IEnumerable<ContactRequestDto>>(requests);
        }

        public async Task<Result<ContactRequestDto>> CreateAsync(CreateContactRequestDto requestDto)
        {
            var owner = await _unitOfWork.AppUsers.GetQueryable().FirstOrDefaultAsync(x => x.Id == requestDto.PropertyOwnerId);
            if (owner == null)
            {
                return Result<ContactRequestDto>.Failure("Không tìm thấy thông tin chủ bài đăng", ErrorType.NotFound);
            }

            var property = await _unitOfWork.Properties.GetQueryable().FirstOrDefaultAsync(x => x.Id == requestDto.PropertyId);
            if (property == null)
            {
                return Result<ContactRequestDto>.Failure("Không tìm thấy thông tin bất động sản", ErrorType.NotFound);
            }

            var contactRequest = _mapper.Map<ContactRequest>(requestDto);

            contactRequest.SentAt = DateTime.UtcNow;
            contactRequest.Status = EnumValues.ContactRequestStatus.Pending.ToString();

            await _unitOfWork.ContactRequests.AddAsync(contactRequest);
            await _unitOfWork.SaveChangesAsync();

            var actionUrl = Helper.ConstructActionUrl(
                            EnumValues.NotificationType.NewContactRequest,
                            requestDto.PropertyId,
                            contactRequest.Id);

            // Send notification for property owner            
            _ = Task.Run(async () =>
            {
                using (var scope = _scopeFactory.CreateScope())
                {
                    try
                    {
                        var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();

                        var request = new NotificationRequest
                        {
                            TargetChannels = NotificationChannel.All,
                            InAppNotificationType = EnumValues.NotificationType.NewContactRequest,
                            RecipientId = requestDto.PropertyOwnerId,
                            RelatedPropertyId = property.Id,
                            RelatedEntityId = contactRequest.Id,
                            RecipientEmail = owner.Email,
                            EmailType = EmailType.ContactNotification.ToString(),
                            Title = $"Bạn có một yêu cầu liên hệ.",
                            Message = $"Bạn có một yêu cầu liên hệ từ {requestDto.Name} ({requestDto.Phone}) cho bài đăng '{property.Name}' của bạn.",
                            Data = new Dictionary<string, string>
                            {
                                { "user_name", owner.FullName },
                                { "listing_title", property.Name },
                                { "contact_person_name", requestDto.Name },
                                { "contact_person_email", requestDto.Email },
                                { "contact_person_phone", requestDto.Phone },
                                { "contact_message", requestDto.Note },
                                { "listing_management_url", actionUrl },
                                { "propertyCode", property.Code.ToString() },
                                {"actionUrl", actionUrl }
                            }
                        };

                        // Gửi đi
                        await notificationService.SendAsync(request);
                    }
                    catch (Exception ex)
                    {
                        var logger = scope.ServiceProvider.GetRequiredService<ILogger<ContactRequestService>>();
                        logger.LogError(ex, $"Lỗi khi gửi notification trong tác vụ nền cho property {property.Id}");
                    }
                }
            });

            return Result<ContactRequestDto>.Success(_mapper.Map<ContactRequestDto>(contactRequest));
        }

        public async Task<Result> UpdateAsync(Guid id, UpdateContactRequestDto requestDto)
        {
            var request = await _unitOfWork.ContactRequests.GetByIdAsync(id);
            if (request == null) return Result.Failure(Shared.Constants.ContactRequestMessage.ContactRequestNotFound, ErrorType.NotFound);

            _mapper.Map(requestDto, request);
            request.UpdatedAt = DateTime.UtcNow;

            _unitOfWork.ContactRequests.Update(request);
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public async Task<Result> DeleteAsync(Guid id)
        {
            var request = await _unitOfWork.ContactRequests.GetByIdAsync(id);
            if (request == null) return Result.Failure(Shared.Constants.ContactRequestMessage.ContactRequestNotFound, ErrorType.NotFound);

            request.IsDeleted = true;
            request.DeletedAt = DateTime.UtcNow;

            _unitOfWork.ContactRequests.Update(request);
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }
    }

}
