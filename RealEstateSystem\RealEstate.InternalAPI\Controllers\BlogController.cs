﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.CustomModel;

namespace RealEstate.InternalAPI.Controllers
{
    [Route("api/[controller]")]
    public class BlogController : BaseController
    {
        private readonly IBlogService _blogPostService;

        public BlogController(IBlogService blogPostService)
        {
            _blogPostService = blogPostService;
        }

        [HttpGet("blog-posts")]
        public async Task<IActionResult> GetBlogPosts([FromQuery] PagingRequest request, [FromQuery] string? title)
        {
            var result = await _blogPostService.GetBlogAsync(request, title ?? string.Empty);
            return HandleResult(result);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBlogPostById(Guid id)
        {
            var blogPost = await _blogPostService.GetBlogByIdAsync(id);
            return HandleResult(blogPost);
        }

        [HttpPost]
        public async Task<IActionResult> CreateBlog(CreateBlogPostDto blogDto)
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return Unauthorized("User không hợp lệ");
            }

            var createdBlogResult = await _blogPostService.CreateBlogAsync(blogDto, userId.Value);
            return HandleResult(createdBlogResult);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateBlog(Guid id, CreateBlogPostDto blogDto)
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return Unauthorized("User không hợp lệ");
            }

            var result = await _blogPostService.UpdateBlogAsync(id, blogDto, userId.Value);
            return HandleResult(result);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteBlog(Guid id)
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return Unauthorized("User không hợp lệ");
            }

            var result = await _blogPostService.DeleteBlogAsync(id, userId.Value);
            return HandleResult(result);
        }
    }
}
