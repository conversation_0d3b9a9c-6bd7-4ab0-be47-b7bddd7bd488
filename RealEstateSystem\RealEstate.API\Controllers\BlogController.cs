﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.CustomModel;

namespace RealEstate.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class BlogController : BaseController
    {
        private readonly IBlogService _blogPostService;

        public BlogController(IBlogService blogPostService)
        {
            _blogPostService = blogPostService;
        }

        [AllowAnonymous]
        [HttpGet("slug/{slug}")]
        public async Task<IActionResult> GetBlogPostBySlug(string slug)
        {
            var result = await _blogPostService.GetBlogBySlugAsync(slug);
            return HandleResult(result);
        }

        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> GetAllBlogPost()
        {
            var result = await _blogPostService.GetAllBlogAsync();
            return HandleResult(result);
        }

        [HttpGet("blog-posts")]
        [AllowAnonymous]
        public async Task<IActionResult> GetBlogPosts([FromQuery] PagingRequest request, [FromQuery] string? title)
        {
            var result = await _blogPostService.GetBlogAsync(request, title ?? string.Empty);
            return HandleResult(result);
        }
    }
}
