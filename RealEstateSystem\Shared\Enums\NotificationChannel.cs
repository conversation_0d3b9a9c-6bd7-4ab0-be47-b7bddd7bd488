﻿namespace Shared.Enums
{
    [Flags]
    public enum NotificationChannel
    {
        None = 0,
        InApp = 1,      // G<PERSON>i thông báo trong ứng dụng (lưu vào DB, có thể bắn SignalR)
        Push = 2,       // G<PERSON><PERSON> thông báo đẩy qua Firebase/APNS
        Email = 4,      // G<PERSON>i qua Email
        SMS = 8,        // G<PERSON><PERSON> qua SMS

        // T<PERSON> hợp thường dùng
        All = InApp | Push | Email | SMS
    }
}
