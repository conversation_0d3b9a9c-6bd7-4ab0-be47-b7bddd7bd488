using System.ComponentModel.DataAnnotations;

namespace RealEstate.Application.DTO.Analytics
{
    public class LogPropertyViewDto
    {
        [Required]
        public Guid PropertyId { get; set; }
        
        public Guid? ViewerId { get; set; }
        
        [Required]
        [StringLength(50)]
        public string ViewerIp { get; set; } = string.Empty;
        
        [Required]
        public string UserAgent { get; set; } = string.Empty;
        
        public string? ReferrerUrl { get; set; }
        
        [StringLength(255)]
        public string? SessionId { get; set; }
        
        [StringLength(20)]
        public string? DeviceType { get; set; }

        [StringLength(255)]
        public string? DeviceId { get; set; }
        
        [StringLength(50)]
        public string? Platform { get; set; }
        
        [StringLength(50)]
        public string? Browser { get; set; }
        
        [StringLength(100)]
        public string? City { get; set; }
        
        [StringLength(100)]
        public string? Region { get; set; }
        
        [StringLength(100)]
        public string? Country { get; set; }
    }
} 