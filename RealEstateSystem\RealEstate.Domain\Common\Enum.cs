using System.ComponentModel;

namespace RealEstate.Domain.Common
{
    public static class EnumValues
    {
        public enum UserType
        {
            Seller,
            Buyer,
            Admin
        }

        public enum UserRoleCode
        {
            [Description("Super Mod")]
            SUPER_MOD,

            [Description("System Admin")]
            SYSTEM_ADMIN,

            [Description("Admin Duyệt Bài")]
            ADMIN_APPROVER,

            [Description("Admin Nội Dung")]
            ADMIN_CONTENT,

            [Description("Kế Toán Trưởng")]
            FINANCE_MANAGER,

            [Description("Kế Toán Viên")]
            FINANCE_STAFF
        }

        public enum PermissionCode
        {
            // Admin & User Management
            [Description("P_USER_MANAGE_FULL")]
            P_USER_MANAGE_FULL,
            [Description("P_VIEW_USER_MANAGEMENT_SCREEN")]
            P_VIEW_USER_MANAGEMENT_SCREEN,

            // Listing Management
            [Description("P_LISTING_APPROVE")]
            P_LISTING_APPROVE,
            [Description("P_LISTING_MANAGE")] // Quyền sửa/xóa tin đăng
            P_LISTING_MANAGE,
            [Description("P_LISTING_VIEW_PRICE")] // Quyền xem giá tin đăng
            P_LISTING_VIEW_PRICE,

            // News Content Management
            [Description("P_NEWS_MANAGE")]
            P_NEWS_MANAGE,
            [Description("P_VIEW_NEWS_EDITOR_SCREEN")]
            P_VIEW_NEWS_EDITOR_SCREEN,

            // Accounting & Finance
            [Description("P_ACCOUNTING_VIEW")]
            P_ACCOUNTING_VIEW,
            [Description("P_ACCOUNTING_MANAGE")] // Quyền quản lý các tác vụ kế toán (nếu có)
            P_ACCOUNTING_MANAGE,
            [Description("P_INVOICE_EXPORT_E")]
            P_INVOICE_EXPORT_E,
            [Description("P_REPORT_FINANCE_VIEW")]
            P_REPORT_FINANCE_VIEW,
            [Description("P_INVOICE_REQUEST_VIEW")]
            P_INVOICE_REQUEST_VIEW,

            // All access / Super Mod specific
            [Description("P_ALL_ACCESS")] // Có thể không cần policy riêng nếu Super Mod được kiểm tra bằng RoleCode
            P_ALL_ACCESS
        }

        public enum PropertyStatus
        {
            Draft,
            PendingApproval,
            Approved,
            RejectedByAdmin,
            RejectedDueToUnpaid,
            WaitingPayment,
            Expired,
            Sold
        }

        public enum PostType
        {
            Sale,
            Rent,
        }

        public enum PropertyType
        {
            can_ho,
            nha_pho,
            nha_tro,
        }

        public enum ContactRequestStatus
        {
            Pending, // Chưa xử lý.
            Read, //Đã liên hệ khách hàng.
        }

        public enum NotificationCategory
        {
            Listing,       // Tin đăng
            Finance,       // Tài chính
            Promotion,     // Khuyến mãi
            Account,       // Tài khoản
            Miscellaneous  // Khác
        }

        public enum NotificationType
        {
            // Category: Listing (Tin đăng)
            ListingCreated,           // Tin đăng mới được tạo
            ListingApproved,          // Tin đăng được duyệt
            ListingRejected,          // Tin đăng bị từ chối
            ListingExpired,           // Tin đăng sắp hết hạn / đã hết hạn
            NewContactRequest,        // Có yêu cầu liên hệ mới cho tin đăng

            // Category: Finance (Tài chính)
            WalletTopUpSuccess,       // Nạp tiền vào ví thành công
            WalletTopUpFailed,        // Nạp tiền thất bại
            ServicePaymentSuccess,    // Thanh toán dịch vụ thành công
            LowBalanceWarning,        // Cảnh báo số dư sắp hết

            // Category: Promotion (Khuyến mãi)
            NewDiscountAvailable,     // Có chương trình khuyến mãi mới
            PromotionalCodeReceived,  // Bạn nhận được một mã giảm giá

            // Category: Account (Tài khoản)
            WelcomeUser,              // Chào mừng thành viên mới
            PasswordResetRequest,     // Yêu cầu đặt lại mật khẩu
            AccountSecurityAlert,     // Cảnh báo đăng nhập lạ

            // Category: Miscellaneous (Khác)
            SystemMaintenance,        // Thông báo bảo trì hệ thống (gửi cho tất cả user)
            FeatureAnnouncement,      // Thông báo về tính năng mới
            GeneralSystemMessage      // Thông báo chung khác
        }

        public enum TransactionType
        {
            DEPOSIT,
            SPEND,
            SPEND_POST,
            SPEND_HIGHLIGHT
        }

        public enum TransactionStatus
        {
            COMPLETED,
            PENDING,
            FAILED,
            CANCELLED
        }

        public enum PropertyEngagementEventType
        {
            [Description("view")]
            View,
            [Description("favorite")]
            Favorite,
            [Description("unfavorite")]
            Unfavorite,
            [Description("click_phone")]
            ClickPhone,
            [Description("chat")]
            Chat,
            [Description("contact")]
            Contact,
            [Description("share")]
            Share,
            [Description("search_impression")]
            SearchImpression,
            [Description("click_through")]
            ClickThrough,
            [Description("conversion")]
            Conversion,
            [Description("print")]
            Print,
            [Description("save_for_later")]
            SaveForLater,
            [Description("report_listing")]
            ReportListing,
            [Description("view_map")]
            ViewMap,
            [Description("view_gallery")]
            ViewGallery,
            [Description("view_floor_plan")]
            ViewFloorPlan,
            [Description("view_video")]
            ViewVideo
        }

        public const int UPDATE_DEFAULT_REMAINING_TIMES = 5;
    }
}
