using System.ComponentModel.DataAnnotations;

namespace RealEstate.Application.DTO
{
    public class CreateAdminUserDto
    {
        [Required]
        public string FullName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        [MinLength(6)]
        public string Password { get; set; } = string.Empty;

        [Required]
        public string Phone { get; set; } = string.Empty;

        [Required]
        public List<Guid> RoleIds { get; set; } = new List<Guid>();

        public string GetNormalizedEmail() => Email.Trim().ToLowerInvariant();
    }
}
