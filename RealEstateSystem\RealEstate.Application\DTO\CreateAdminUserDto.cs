using System.ComponentModel.DataAnnotations;

namespace RealEstate.Application.DTO
{
    public class CreateAdminUserDto
    {
        [Required]
        public string FullName { get; set; }
        
        [Required]
        [EmailAddress]
        public string Email { get; set; }
        
        [Required]
        [MinLength(6)]
        public string Password { get; set; }
        
        [Required]
        public string Phone { get; set; }
        
        [Required]
        public List<Guid> RoleIds { get; set; } = new List<Guid>();

        public string GetNormalizedEmail() => Email?.Trim().ToLowerInvariant();
    }
}
