using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using Shared.Results;

namespace RealEstate.InternalAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Policy = "UserAdminExists")]
    public class RolePermissionController : BaseController
    {
        private readonly IRolePermissionService _rolePermissionService;

        public RolePermissionController(IRolePermissionService rolePermissionService)
        {
            _rolePermissionService = rolePermissionService;
        }

        #region Role Management

        /// <summary>
        /// Get all roles in the system
        /// </summary>
        /// <returns>List of all roles</returns>
        [HttpGet("roles")]
        [Authorize(Policy = "CanManageUsers")]
        public async Task<IActionResult> GetAllRoles()
        {
            var roles = await _rolePermissionService.GetAllRolesAsync();
            return HandleResult(roles);
        }

        /// <summary>
        /// Get role by ID with its permissions
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <returns>Role with permissions</returns>
        [HttpGet("roles/{roleId}")]
        [Authorize(Policy = "CanManageUsers")]
        public async Task<IActionResult> GetRoleById(Guid roleId)
        {
            var role = await _rolePermissionService.GetRoleByIdAsync(roleId);
            return HandleResult(role);
        }

        /// <summary>
        /// Create a new role
        /// </summary>
        /// <param name="createRoleDto">Role creation data</param>
        /// <returns>Created role</returns>
        [HttpPost("roles")]
        [Authorize(Policy = "CanManageUsers")]
        public async Task<IActionResult> CreateRole([FromBody] CreateAdminRoleDto createRoleDto)
        {
            var role = await _rolePermissionService.CreateRoleAsync(createRoleDto);
            return HandleResult(role);
        }

        /// <summary>
        /// Update an existing role
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <param name="updateRoleDto">Role update data</param>
        /// <returns>Updated role</returns>
        [HttpPut("roles/{roleId}")]
        [Authorize(Policy = "CanManageUsers")]
        public async Task<IActionResult> UpdateRole(Guid roleId, [FromBody] CreateAdminRoleDto updateRoleDto)
        {
            var role = await _rolePermissionService.UpdateRoleAsync(roleId, updateRoleDto);
            return HandleResult(role);
        }

        /// <summary>
        /// Delete a role (only if no users are assigned to it)
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <returns>Status of the operation</returns>
        [HttpDelete("roles/{roleId}")]
        [Authorize(Policy = "CanManageUsers")]
        public async Task<IActionResult> DeleteRole(Guid roleId)
        {
            var result = await _rolePermissionService.DeleteRoleAsync(roleId);
            return HandleResult(result);
        }

        #endregion

        #region Permission Management

        /// <summary>
        /// Get all permissions in the system
        /// </summary>
        /// <returns>List of all permissions</returns>
        [HttpGet("permissions")]
        [Authorize(Policy = "CanManageUsers")]
        public async Task<IActionResult> GetAllPermissions()
        {
            var permissions = await _rolePermissionService.GetAllPermissionsAsync();
            return HandleResult(permissions);
        }

        /// <summary>
        /// Get permissions assigned to a specific role
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <returns>List of permissions for the role</returns>
        [HttpGet("roles/{roleId}/permissions")]
        [Authorize(Policy = "CanManageUsers")]
        public async Task<IActionResult> GetRolePermissions(Guid roleId)
        {
            var permissions = await _rolePermissionService.GetRolePermissionsAsync(roleId);
            return HandleResult(permissions);
        }

        #endregion

        #region Role Permission Assignment

        /// <summary>
        /// Assign permissions to a role (replaces existing permissions)
        /// </summary>
        /// <param name="assignPermissionsDto">Role and permission assignment data</param>
        /// <returns>Status of the operation</returns>
        [HttpPost("roles/assign-permissions")]
        [Authorize(Policy = "CanManageUsers")]
        public async Task<IActionResult> AssignPermissionsToRole([FromBody] AssignPermissionsToRoleDto assignPermissionsDto)
        {
            var result = await _rolePermissionService.AssignPermissionsToRoleAsync(assignPermissionsDto);
            return HandleResult(result);
        }

        /// <summary>
        /// Add a single permission to a role
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <param name="permissionId">Permission ID to add</param>
        /// <returns>Status of the operation</returns>
        [HttpPost("roles/{roleId}/permissions/{permissionId}")]
        [Authorize(Policy = "CanManageUsers")]
        public async Task<IActionResult> AddPermissionToRole(Guid roleId, Guid permissionId)
        {
            var result = await _rolePermissionService.AddPermissionToRoleAsync(roleId, permissionId);
            return HandleResult(result);
        }

        /// <summary>
        /// Remove a single permission from a role
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <param name="permissionId">Permission ID to remove</param>
        /// <returns>Status of the operation</returns>
        [HttpDelete("roles/{roleId}/permissions/{permissionId}")]
        [Authorize(Policy = "CanManageUsers")]
        public async Task<IActionResult> RemovePermissionFromRole(Guid roleId, Guid permissionId)
        {
            var result = await _rolePermissionService.RemovePermissionFromRoleAsync(roleId, permissionId);
            return HandleResult(result);
        }

        #endregion
    }
}
