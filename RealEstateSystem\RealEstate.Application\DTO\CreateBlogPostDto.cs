﻿using System.ComponentModel.DataAnnotations;

namespace RealEstate.Application.DTO
{
    public class CreateBlogPostDto
    {
        [Required]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;

        [Required]
        public string Content { get; set; } = string.Empty;

        public string? FeaturedImage { get; set; }
        public string? Tags { get; set; }
        public string? Status { get; set; }
    }
}
