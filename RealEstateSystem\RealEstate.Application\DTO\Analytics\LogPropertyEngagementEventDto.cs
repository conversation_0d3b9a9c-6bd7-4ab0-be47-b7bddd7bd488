using System.ComponentModel.DataAnnotations;

namespace RealEstate.Application.DTO.Analytics
{
    public class LogPropertyEngagementEventDto
    {
        [Required]
        public Guid PropertyId { get; set; }
        
        public Guid? UserId { get; set; }
        
        [Required]
        [StringLength(50)]
        public string EventType { get; set; } = string.Empty;
        
        [StringLength(255)]
        public string? SessionId { get; set; }

        [StringLength(255)]
        public string? DeviceId { get; set; }

        public string? UserAgent { get; set; }
        
        [StringLength(50)]
        public string? IpAddress { get; set; }
        
        [StringLength(20)]
        public string? DeviceType { get; set; }
        
        [StringLength(50)]
        public string? Platform { get; set; }
        
        [StringLength(50)]
        public string? Browser { get; set; }
        
        [StringLength(100)]
        public string? City { get; set; }
        
        [StringLength(100)]
        public string? Region { get; set; }
        
        [StringLength(100)]
        public string? Country { get; set; }
    }
} 