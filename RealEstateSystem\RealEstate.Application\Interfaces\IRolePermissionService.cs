using RealEstate.Application.DTO;
using Shared.Results;

namespace RealEstate.Application.Interfaces
{
    public interface IRolePermissionService
    {
        #region Role Management
        
        /// <summary>
        /// Get all roles in the system
        /// </summary>
        /// <returns>List of all roles</returns>
        Task<Result<IEnumerable<AdminRoleDto>>> GetAllRolesAsync();

        /// <summary>
        /// Get role by ID with its permissions
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <returns>Role with permissions</returns>
        Task<Result<AdminRoleDto>> GetRoleByIdAsync(Guid roleId);

        /// <summary>
        /// Create a new role
        /// </summary>
        /// <param name="createRoleDto">Role creation data</param>
        /// <returns>Created role</returns>
        Task<Result<AdminRoleDto>> CreateRoleAsync(CreateAdminRoleDto createRoleDto);

        /// <summary>
        /// Update an existing role
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <param name="updateRoleDto">Role update data</param>
        /// <returns>Updated role</returns>
        Task<Result<AdminRoleDto>> UpdateRoleAsync(Guid roleId, CreateAdminRoleDto updateRoleDto);

        /// <summary>
        /// Delete a role (only if no users are assigned to it)F
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <returns>True if deleted successfully</returns>
        Task<Result> DeleteRoleAsync(Guid roleId);

        #endregion

        #region Permission Management

        /// <summary>
        /// Get all permissions in the system
        /// </summary>
        /// <returns>List of all permissions</returns>
        Task<Result<IEnumerable<PermissionDto>>> GetAllPermissionsAsync();

        /// <summary>
        /// Get permissions assigned to a specific role
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <returns>List of permissions for the role</returns>
        Task<Result<IEnumerable<PermissionDto>>> GetRolePermissionsAsync(Guid roleId);

        #endregion

        #region Role Permission Assignment

        /// <summary>
        /// Assign permissions to a role (replaces existing permissions)
        /// </summary>
        /// <param name="assignPermissionsDto">Role and permission assignment data</param>
        /// <returns>True if successful</returns>
        Task<Result> AssignPermissionsToRoleAsync(AssignPermissionsToRoleDto assignPermissionsDto);

        /// <summary>
        /// Add a single permission to a role
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <param name="permissionId">Permission ID</param>
        /// <returns>True if successful</returns>
        Task<Result> AddPermissionToRoleAsync(Guid roleId, Guid permissionId);

        /// <summary>
        /// Remove a permission from a role
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <param name="permissionId">Permission ID</param>
        /// <returns>True if successful</returns>
        Task<Result> RemovePermissionFromRoleAsync(Guid roleId, Guid permissionId);

        #endregion
    }
}
