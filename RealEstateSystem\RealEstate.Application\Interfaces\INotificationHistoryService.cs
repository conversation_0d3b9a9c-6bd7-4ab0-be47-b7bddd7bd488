﻿using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Notification;
using Shared.Results;

namespace RealEstate.Application.Interfaces
{
    public interface INotificationHistoryService
    {
        // GET notification
        Task<Result<NotificationDto>> GetNotificationByIdAsync(Guid id);        

        Task<Result<PagedResultDto<NotificationDto>>> GetNotificationsForUserAsync(
            Guid userId,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            int page = 1,
            int pageSize = 50);

        Task<Result<PagedResultDto<NotificationDto>>> GetNotificationsByCategoryAsync(
            string? category = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            int page = 1,
            int pageSize = 50,
            bool getOnlySystem = true);

        Task<Result<PagedResultDto<NotificationDto>>> GetNotificationsForUserAsync(
            Guid userId,
            string? category = null, 
            bool? unreadOnly = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            int page = 1,
            int pageSize = 50);

        Task<Result<int>> GetUnreadNotificationCountAsync(Guid userId);

        // CHANGE notification status
        Task<Result> MarkAllAsReadAsync(Guid userId);
        Task<Result> MarkAsReadAsync(Guid id, Guid userId);
        Task<Result> MarkMultipleAsReadAsync(List<Guid> notificationIds, Guid userId);

        // Creates notification
        Task<Result<NotificationDto>> CreateNotificationAsync(CreateNotificationDto notificationDto);

        // Deletes notification
        Task<Result> DeleteNotificationAsync(Guid id, Guid userId);
    }
}
