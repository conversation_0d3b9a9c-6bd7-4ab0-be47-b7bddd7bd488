﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Infrastructure.Services.FileStorage;

namespace RealEstate.API.Controllers
{
    [Route("[controller]")]
    [Authorize]
    public class MediaController : BaseController
    {
        private readonly IMediaServices _mediaServices;
        private readonly IFileStorageService _fileStorageService;

        public MediaController(IMediaServices mediaServices, IFileStorageService fileStorageService)
        {
            _mediaServices = mediaServices;
            _fileStorageService = fileStorageService;
        }

        [HttpGet("{fileId}")]
        [AllowAnonymous]
        public async Task<IActionResult> GetMedia(Guid fileId, [FromQuery] string size = null)
        {
            var mediaResult = await _mediaServices.GetMediaByIdAsync(fileId);
            if (!mediaResult.IsSuccess)
                return HandleResult(mediaResult);

            var media = mediaResult.Value;
            string filePath = _mediaServices.GetMediaPath(media, size);

            if (string.IsNullOrEmpty(filePath))
            {
                return NotFound(new { Message = "Media file path not found" });
            }

            // Check if we're using S3 storage
            if (_fileStorageService is S3StorageService s3Service)
            {
                try
                {
                    // For S3, get the file stream directly
                    var fileStream = await s3Service.GetFileStreamAsync(filePath);
                    return File(fileStream, media.MediaType!);
                }
                catch (Exception)
                {
                    return NotFound(new { Message = "Media file not found in S3" });
                }
            }
            else
            {
                // For local storage, check if file exists and return file stream
                if (!await _fileStorageService.FileExistsAsync(filePath))
                {
                    return NotFound(new { Message = "Media file not found" });
                }

                var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                return File(fileStream, media.MediaType!);
            }
        }

        [HttpPut("update-caption")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> UpdateCaption([FromBody] UpdatePropertyMediaCaptionDto updateDto)
        {
            var result = await _mediaServices.UpdateMediaCaptionAsync(updateDto.Id, updateDto.Caption);
            return HandleResult(result);
        }

        [HttpPut("update-is-avatar")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> UpdateIsAvatar([FromBody] UpdatePropertyMediaIsAvatarDto updateDto)
        {
            var result = await _mediaServices.UpdateMediaIsAvatarAsync(updateDto.Id, updateDto.IsAvatar);
            return HandleResult(result);
        }

        [HttpDelete("{id}")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> DeleteMedia(Guid id)
        {
            var result = await _mediaServices.DeleteMediaAsync(id);
            return HandleResult(result);
        }
    }
}
