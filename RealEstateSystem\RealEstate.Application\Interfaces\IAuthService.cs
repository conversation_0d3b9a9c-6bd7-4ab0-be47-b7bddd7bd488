﻿using RealEstate.Application.DTO;
using Shared.Results;

namespace RealEstate.Application.Interfaces
{
    public interface IAuthService
    {
        Task<Result<UserDto>> RegisterAsync(CreateUserDto registerDto);
        Task<Result<UserDto>> LoginAsync(LoginDto loginDto);
        Task<Result<UserDto>> RefreshToken(Guid userId);
        Task<Result> ChangePassword(ChangePasswordDto changePasswordDto);
        Task<bool> ValidateUserCredentialsAsync(string email, string password);
    }
}
