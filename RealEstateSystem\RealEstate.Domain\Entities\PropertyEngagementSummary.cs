using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RealEstate.Domain.Entities
{
    public class PropertyEngagementSummary : BaseEntity
    {
        [Required]
        public Guid PropertyId { get; set; }
        
        [Required]
        public int TotalViews { get; set; } = 0;
        
        [Required]
        public int TotalFavorites { get; set; } = 0;
        
        [Required]
        [Column(TypeName = "numeric(20,2)")]
        public decimal TotalSpent { get; set; } = 0;
        
        [Required]
        [Column(TypeName = "numeric(20,2)")]
        public decimal ExtensionSpent { get; set; } = 0;
        
        [Required]
        [Column(TypeName = "numeric(20,2)")]
        public decimal HighlightSpent { get; set; } = 0;
        
        [Required]
        public DateTime LastUpdatedAt { get; set; } = DateTime.UtcNow;
        
        // New columns from migration
        [Required]
        public int TotalClicksPhone { get; set; } = 0;
        
        [Required]
        public int TotalClicksChat { get; set; } = 0;
        
        [Required]
        public int TotalSearchImpressions { get; set; } = 0;
        
        [Required]
        public int TotalClickThroughs { get; set; } = 0;
        
        [Required]
        public int ConversionCount { get; set; } = 0;
        
        public DateTime? LastViewedAt { get; set; }
        
        public int? AverageViewDuration { get; set; } // in seconds
        
        // Navigation properties
        [ForeignKey("PropertyId")]
        public Property? Property { get; set; }
    }
}
