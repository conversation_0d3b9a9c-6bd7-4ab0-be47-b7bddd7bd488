﻿namespace RealEstate.Application.Interfaces
{
    public class GatewayResponse
    {
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; }
        public string PaymentUrl { get; set; }
    }

    public class GatewayRequest
    {
        public decimal Amount { get; set; } 
        public string TransactionReference { get; set; }
        public string ReturnUrl { get; set; }
        public string NotifyUrl { get; set; }
    }

    public interface IPaymentGatewayService
    {
        Task<GatewayResponse> CreatePaymentRequestAsync(GatewayRequest request);
    }
}
