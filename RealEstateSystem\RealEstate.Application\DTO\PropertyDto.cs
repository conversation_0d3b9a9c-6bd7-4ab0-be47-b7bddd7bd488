﻿using RealEstate.Domain.Entities;

namespace RealEstate.Application.DTO
{
    public class PropertyDto
    {
        public Guid Id { get; set; }
        public required string Name { get; set; }
        public string? Slug { get; set; }
        public string? PropertyType { get; set; }
        public string? PostType { get; set; }
        public int? CityId { get; set; }
        public int? DistrictId { get; set; }
        public int? StreetId { get; set; }
        public int? WardId { get; set; }
        public required string Address { get; set; }
        public decimal? Area { get; set; }
        public required decimal Price { get; set; }
        public string? VideoUrl { get; set; }
        public int? Floors { get; set; }
        public int? Rooms { get; set; }
        public int? Toilets { get; set; }
        public string? Direction { get; set; }
        public string? BalconyDirection { get; set; }
        public string? Legality { get; set; }
        public string? Interior { get; set; }
        public int? Width { get; set; }
        public int? RoadWidth { get; set; }
        public required string Description { get; set; }
        public string? Overview { get; set; }
        public string? PlaceData { get; set; }
        public string? Policies { get; set; }
        public string? Neighborhood { get; set; }
        public string? Status { get; set; }
        public decimal PostPrice { get; set; }
        public bool IsHighlighted { get; set; }
        public bool IsAutoRenew { get; set; }
        public DateTime ExpiresAt { get; set; }
        public int? UpdateRemainingTimes { get; set; }
        public List<PropertyMediaDtoResponse>? PropertyMedia { get; set; }
        public Guid OwnerId { get; set; }

        public UserInformationDto Owner { get; set; }

        public DateTime CreatedAt { get; set; }

        // Tọa độ góc Tây Nam (SouthWest)
        public decimal? SwLat { get; set; }
        public decimal? SwLng { get; set; }

        // Tọa độ góc Đông Bắc (NorthEast)
        public decimal? NeLat { get; set; }
        public decimal? NeLng { get; set; }

        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
        public long Code { get; set; }
    }

}
