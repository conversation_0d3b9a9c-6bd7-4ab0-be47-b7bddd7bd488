﻿using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Notification;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Common;
using Shared.Results;

namespace RealEstate.InternalAPI.Controllers
{
    [Route("api/[controller]")]
    /// <summary>
    /// Controller for managing user notifications
    /// </summary>
    public class NotificationController : BaseController
    {
        private readonly INotificationHistoryService _notificationHistoryService;
        private readonly ILogger<NotificationController> _logger;

        /// <summary>
        /// Initializes a new instance of the NotificationController
        /// </summary>
        /// <param name="notificationHistoryService">The notification service</param>
        /// <param name="notificationPreferenceService">The notification preference service</param>
        /// <param name="logger">The logger</param>
        public NotificationController(
            INotificationHistoryService notificationHistoryService,
            ILogger<NotificationController> logger)
        {
            _notificationHistoryService = notificationHistoryService;
            _logger = logger;
        }

        /// <summary>
        /// Get notifications by type for a specific user with pagination and date filtering (Admin only)
        /// </summary>
        /// <param name="type">The notification type</param>
        /// <param name="userId">The user ID</param>
        /// <param name="fromDate">Optional start date filter</param>
        /// <param name="toDate">Optional end date filter</param>
        /// <param name="page">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 10)</param>
        /// <returns>Paged list of notifications of the specified type for the specified user</returns>
        /// <response code="200">Returns the notifications</response>
        /// <response code="400">If the notification type is invalid</response>
        /// <response code="403">If the user is not an admin</response>
        /// <response code="500">If there was an internal server error</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(PagedResultDto<NotificationDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetNotification(
            [FromQuery] string category = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50)
        {
            if (!string.IsNullOrEmpty(category) && !FunctionHelper.IsValidNotificationCategory(category))
            {
                return BadRequest("Invalid notification type");
            }

            var result = await _notificationHistoryService.GetNotificationsByCategoryAsync(
                category,
                fromDate,
                toDate,
                page,
                pageSize);

            return HandleResult(result);
        }

        /// <summary>
        /// Create a new notification
        /// </summary>
        /// <param name="createNotificationDto">The notification data</param>
        /// <returns>The created notification</returns>
        /// <response code="201">Returns the newly created notification</response>
        /// <response code="400">If the request data is invalid</response>
        /// <response code="403">If the user is not authorized to create notifications</response>
        /// <response code="500">If there was an internal server error</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(NotificationDto))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Create([FromBody] CreateNotificationDto createNotificationDto)
        {
            // Validate the input
            var validationResult = ValidateCreateNotificationRequest(createNotificationDto);
            if (validationResult.IsFailure)
            {
                return HandleResult(validationResult);
            }
            // Create the notification
            var createdNotificationResult = await _notificationHistoryService.CreateNotificationAsync(createNotificationDto);

            return HandleResult(createdNotificationResult);
        }

        /// <summary>
        /// Validates the create notification request
        /// </summary>
        /// <param name="createNotificationDto">The notification data to validate</param>
        /// <returns>BadRequest result if validation fails, null if validation passes</returns>
        private Result ValidateCreateNotificationRequest(CreateNotificationDto createNotificationDto)
        {
            if (createNotificationDto == null)
                return Result.Failure("Notification data is required", Shared.Enums.ErrorType.Validation);

            if (string.IsNullOrWhiteSpace(createNotificationDto.Type))
                return Result.Failure("Notification type is required", Shared.Enums.ErrorType.Validation);

            if (string.IsNullOrWhiteSpace(createNotificationDto.Category))
                return Result.Failure("Notification type is required", Shared.Enums.ErrorType.Validation);

            if (string.IsNullOrWhiteSpace(createNotificationDto.Title))
                return Result.Failure("Notification title is required", Shared.Enums.ErrorType.Validation);

            if (string.IsNullOrWhiteSpace(createNotificationDto.Message))
                return Result.Failure("Notification message is required", Shared.Enums.ErrorType.Validation);

            if (!FunctionHelper.IsValidNotificationCategory(createNotificationDto.Category))
                return Result.Failure("Invalid notification type. Valid types are: System, Transaction, Contact, Promotion, News, WalletUpdate, CustomerMessage", Shared.Enums.ErrorType.Validation);

            // Check if user has admin role for system-wide notification types
            if (RequiresAdminRole(createNotificationDto.Type) && !IsUserAdmin())
                return Result.Failure("Only administrators can create System, Promotion, and News notifications", Shared.Enums.ErrorType.Forbidden);

            return Result.Success(); // Validation passed
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(Guid id)
        {
            var result = await _notificationHistoryService.GetNotificationByIdAsync(id);
            return HandleResult(result);
        }

        /// <summary>
        /// Checks if the current user has admin role
        /// </summary>
        /// <returns>True if user is admin, false otherwise</returns>
        private bool IsUserAdmin()
        {
            return User.IsInRole(EnumValues.UserRoleCode.SUPER_MOD.ToString()) 
                || User.IsInRole(EnumValues.UserRoleCode.ADMIN_CONTENT.ToString());
        }

        /// <summary>
        /// Checks if the notification type requires admin role
        /// </summary>
        /// <param name="notificationCategory">The notification category to check</param>
        /// <returns>True if admin role is required, false otherwise</returns>
        private bool RequiresAdminRole(string notificationCategory)
        {
            var adminRequiredTypes = new[]
            {
                EnumValues.NotificationCategory.Miscellaneous.ToString(),
                EnumValues.NotificationCategory.Promotion.ToString(),
            };

            return adminRequiredTypes.Contains(notificationCategory, StringComparer.OrdinalIgnoreCase);
        }
    }
}
