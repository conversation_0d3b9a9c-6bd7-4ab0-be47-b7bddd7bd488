namespace RealEstate.Application.DTO
{
    public class InvoiceDto
    {
        public Guid Id { get; set; }
        public Guid UserId { get; set; }
        public Guid PropertyId { get; set; }
        public string Type { get; set; }
        public int TotalAmount { get; set; }
        public string Status { get; set; }
        public string? Note { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? PaidAt { get; set; }
        
        // Navigation properties
        public string? UserName { get; set; }
        public string? UserEmail { get; set; }
        public string? PropertyTitle { get; set; }
        public string? PropertyAddress { get; set; }
        
        public ICollection<InvoiceItemDto> InvoiceItems { get; set; } = new List<InvoiceItemDto>();
    }
}
