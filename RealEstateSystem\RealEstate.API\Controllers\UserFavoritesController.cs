using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.DTO.UserFavorite;
using RealEstate.Application.Interfaces;

namespace RealEstate.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class UserFavoritesController : BaseController
    {
        private readonly IUserFavoriteService _favoriteService;

        public UserFavoritesController(IUserFavoriteService favoriteService)
        {
            _favoriteService = favoriteService;
        }

        [HttpGet("favorites")]
        public async Task<IActionResult> GetUserFavorites()
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("User not authenticated");

            var result = await _favoriteService.GetUserFavoritesAsync(userId.Value);
            return HandleResult(result);
        }

        [HttpGet("favorites-with-details")]
        public async Task<IActionResult> GetUserFavoritesWithDetails(
            [FromQuery] decimal? minPrice = null,
            [FromQuery] decimal? maxPrice = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] string? sortBy = "CreatedAt",
            [FromQuery] bool sortDescending = true,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("User not authenticated");

            var filter = new FavoriteFilterDto
            {
                MinPrice = minPrice,
                MaxPrice = maxPrice,
                FromDate = fromDate,
                ToDate = toDate,
                SortBy = sortBy,
                SortDescending = sortDescending,
                Page = page,
                PageSize = pageSize
            };

            var result = await _favoriteService.GetUserFavoritesWithDetailsAsync(userId.Value, filter);
            return HandleResult(result);
        }

        [HttpPost("add")]
        public async Task<IActionResult> AddToFavorites([FromBody] CreateUserFavoriteDto request)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("User not authenticated");

            var result = await _favoriteService.AddToFavoritesAsync(userId.Value, request.PropertyId);
            return HandleResult(result);
        }

        [HttpDelete("remove/{propertyId}")]
        public async Task<IActionResult> RemoveFromFavorites(Guid propertyId)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("User not authenticated");

            var result = await _favoriteService.RemoveFromFavoritesAsync(userId.Value, propertyId);
            return HandleResult(result);
        }

        [HttpPost("check")]
        public async Task<IActionResult> CheckFavoriteStatus([FromBody] FavoriteCheckRequestDto request)
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("User not authenticated");

            var result = await _favoriteService.CheckFavoriteStatusAsync(userId.Value, request.PropertyIds);
            return HandleResult(result);
        }

        [HttpGet("count")]
        public async Task<IActionResult> GetFavoritesCount()
        {
            var userId = GetUserId();
            if (userId == null) return Unauthorized("User not authenticated");
            
            var result = await _favoriteService.GetFavoritesCountAsync(userId.Value);
            return HandleResult(result);
        }
    }
} 