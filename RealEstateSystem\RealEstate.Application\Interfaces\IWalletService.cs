using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Wallet;
using Shared.Results;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RealEstate.Application.Interfaces
{
    public interface IWalletService
    {
        
        /// <summary>
        /// topup for wallet
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<Result<TopUpInitiationDto>> TopUpAsync(Guid userId, TopUpWalletDto request);

        /// <summary>
        /// handle the payment response from payment gateway
        /// </summary>
        /// <param name="transactionId"></param>
        /// <param name="request"></param>
        /// <param name="processedByUserId"></param>
        /// <returns></returns>
        Task<Result> HandlePaymentNotificationAsync(PaymentNotificationDto notification);
        
        /// <summary>
        /// spend money in wallet internal website
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<Result<WalletTransactionDto>> SpendAsync(Guid userId, SpendWalletDto request);
        Task<Result<IEnumerable<WalletTransactionDto>>> GetTransactionsAsync(Guid userId, int page = 1, int pageSize = 50);
        Task<Result<WalletBalanceDto>> GetBalanceAsync(Guid userId);
        Task<Result<WalletTransactionDto>> GetTransactionByIdAsync(Guid transactionId, Guid userId);
        Task<Result<WalletTransactionDto>> GetTransactionByRefIdAsync(string transactionId, Guid userId);
        Task<Result<IEnumerable<WalletTransactionDto>>> GetPendingTransactionsAsync(int page = 1, int pageSize = 50);
        Task<Result<IEnumerable<WalletTransactionDto>>> GetUserPendingTransactionsAsync(Guid userId, int page = 1, int pageSize = 50);
        Task<Result<TransactionSearchResultDto>> SearchTransactionsAsync(TransactionSearchCriteriaDto criteria, Guid? userId = null);
        Task<Result<byte[]>> ExportTransactionsToExcelAsync(TransactionSearchCriteriaDto criteria, Guid? userId = null);
    }
}