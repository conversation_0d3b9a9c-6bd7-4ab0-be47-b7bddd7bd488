﻿using RealEstate.Domain.Entities;

namespace RealEstate.Domain.Interfaces
{
    public interface IAuditableRepository<T> : IRepository<T> where T : BaseEntityWithAuditable
    {
        Task<IEnumerable<T>> GetActiveAsync();

        /// <summary>
        /// Gets a queryable collection of all entities
        /// </summary>
        /// <returns>IQueryable of entities</returns>
        IQueryable<T> GetQueryable();
    }
}
