﻿using RealEstate.Application.DTO;
using Shared.Results;

namespace RealEstate.Application.Interfaces
{
    public interface IMediaServices
    {
        Task<Result<IEnumerable<PropertyMediaDto>>> GetAllMediaAsync(List<Guid> mediaIds);
        Task<Result<PropertyMediaDto>> GetMediaByIdAsync(Guid id);
        Task<Result<IEnumerable<PropertyMediaDto>>> GetAllMediaAsync();
        Task<Result<IEnumerable<PropertyMediaDto>>> GetMediaByPropertyIdAsync(Guid propertyId);
        Task<Result<PropertyMediaDto>> CreateMediaAsync(CreateMediaDto mediaDto);
        Task<Result<PropertyMediaDto>> UpdateMediaAsync(Guid id, CreateMediaDto mediaDto);
        Task<Result<List<Guid>>> UpdateMediaAsync(Guid propertyId, List<Guid> mediaIds);
        Task<Result<Guid>> UpdateMediaAsync(Guid mediaId, Guid propertyId, string filePath, PropertyMediaDto propertyMedia);
        Task<Result> DeleteMediaAsync(Guid id);
        Task<Result<IEnumerable<PropertyMediaDto>>> UploadPropertyImagesAsync(Guid? propertyId, Microsoft.AspNetCore.Http.IFormFileCollection files, string scheme, string host);
        
        Task<Result> UpdateMediaCaptionAsync(Guid id, string caption);
        Task<Result> UpdateMediaIsAvatarAsync(Guid id, bool isAvatar);
        string GetMediaPath(PropertyMediaDto media, string size);
    }
}
