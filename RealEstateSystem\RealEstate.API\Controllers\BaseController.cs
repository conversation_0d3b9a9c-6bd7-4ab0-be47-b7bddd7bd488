﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Shared.Enums;
using Shared.Responses;
using Shared.Results;
using System.Security.Claims;

namespace RealEstate.API.Controllers
{
    [ApiController]
    [Authorize]
    public abstract class BaseController : ControllerBase
    {
        protected Guid? GetUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (Guid.TryParse(userIdClaim, out Guid userId))
            {
                return userId;
            }
            return null;
        }

        protected string GetUserEmail()
        {
            return User.FindFirst(ClaimTypes.Email)?.Value ?? "Unknown";
        }

        protected string GetClientIpAddress()
        {
            return HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
        }

        protected string GetUserAgent()
        {
            return Request.Headers["User-Agent"].ToString();
        }

        protected void LogUserAction(ILogger logger, string action, object? parameters = null)
        {
            var userId = GetUserId();
            var userEmail = GetUserEmail();
            var ipAddress = GetClientIpAddress();

            logger.LogInformation("User action: {Action} by User {UserId} ({UserEmail}) from IP {IpAddress} with parameters {Parameters}",
                action, userId, userEmail, ipAddress, parameters);
        }

        protected void LogSecurityEvent(ILogger logger, string eventType, string details)
        {
            var userId = GetUserId();
            var userEmail = GetUserEmail();
            var ipAddress = GetClientIpAddress();

            logger.LogWarning("Security event: {EventType} - {Details} by User {UserId} ({UserEmail}) from IP {IpAddress}",
                eventType, details, userId, userEmail, ipAddress);
        }

        protected IActionResult HandleResult<T>(Result<T> result)
        {
            if (result.IsFailure)
            {
                var errorResponse = ApiResponse<T>.Failure(result.ErrorMessage ?? Shared.Constants.ResponseMessages.InternalServerError);
                return result.ErrorType switch
                {
                    ErrorType.NotFound => NotFound(errorResponse),
                    ErrorType.Validation => BadRequest(errorResponse),
                    ErrorType.Conflict => Conflict(errorResponse),
                    ErrorType.Unauthorized => Unauthorized(errorResponse),
                    ErrorType.Forbidden => Forbid(),
                    _ => BadRequest(errorResponse)
                };
            }

            return Ok(ApiResponse<T>.Success(result.Value));
        }

        protected IActionResult HandleResult(Result result)
        {
            if (result.IsFailure)
            {
                var errorResponse = ApiResponse<object>.Failure(result.ErrorMessage);
                return result.ErrorType switch
                {
                    ErrorType.NotFound => NotFound(errorResponse),
                    ErrorType.Validation => BadRequest(errorResponse),
                    ErrorType.Conflict => Conflict(errorResponse),
                    ErrorType.Unauthorized => Unauthorized(errorResponse),
                    ErrorType.Forbidden => Forbid(),
                    _ => BadRequest(errorResponse)
                };
            }

            return NoContent();
        }
    }
}
