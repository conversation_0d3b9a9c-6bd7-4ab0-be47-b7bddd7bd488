﻿using System.ComponentModel.DataAnnotations;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.Application.DTO
{
    public class CreateUserDto
    {
        [Required]
        public string FullName { get; set; }
        [Required]
        public string Email { get; set; }
        [Required]
        public string Password { get; set; }
        [Required]
        public UserType UserType { get; set; }
        [Required]
        public string Phone { get; set; }

        public string GetNormalizedEmail() => Email?.Trim().ToLowerInvariant();
    }
}
