﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using Shared.Responses;
using Shared.Results;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.InternalAPI.Controllers
{
    /// <summary>
    /// Controller for managing property listings and related operations
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class PropertyController : BaseController
    {
        private readonly IPropertyService _propertyService;
        private readonly IPropertyAnalyticsService _propertyAnalyticsService;

        // Error message constants
        private const string INVALID_USER_MESSAGE = "User không hợp lệ";

        public PropertyController(IPropertyService propertyService, IPropertyAnalyticsService propertyAnalyticsService)
        {
            _propertyService = propertyService;
            _propertyAnalyticsService = propertyAnalyticsService;
        }

        /// <summary>
        /// Get a property by its ID
        /// </summary>
        /// <param name="propertyId">The unique identifier of the property</param>
        /// <returns>The property details</returns>
        /// <response code="200">Returns the property</response>
        /// <response code="404">If the property is not found</response>
        [HttpGet("{propertyId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetPropertyById(Guid propertyId)
        {
            var property = await _propertyService.GetPropertyByIdAsync(propertyId);
            return HandleResult(property);
        }

        /// <summary>
        /// Delete a property
        /// </summary>
        /// <param name="propertyId">The ID of the property to delete</param>
        /// <returns>No content if successful</returns>
        /// <response code="204">If the property was successfully deleted</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="403">If the user is not authorized to delete this property</response>
        /// <response code="404">If the property is not found</response>
        [HttpDelete("{propertyId}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteProperty(Guid propertyId)
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return Unauthorized(ApiResponse<object>.Failure(INVALID_USER_MESSAGE));
            }

            var result = await _propertyService.DeletePropertyAsync(propertyId, userId.Value);
            return HandleResult(result);
        }

        /// <summary>
        /// Delete multiple properties in bulk
        /// </summary>
        /// <param name="request">The list of property IDs to delete</param>
        /// <returns>No content if successful</returns>
        /// <response code="204">If the properties were successfully deleted</response>
        /// <response code="400">If the request is invalid or some properties couldn't be deleted</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="403">If the user is not authorized to delete one or more properties</response>
        [HttpDelete("bulk")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> DeleteProperties([FromBody] BulkPropertyIdsDto request)
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return Unauthorized(ApiResponse<object>.Failure(INVALID_USER_MESSAGE));
            }

            var result = await _propertyService.DeletePropertiesAsync(request.PropertyIds, userId.Value);
            return HandleResult(result);
        }

        /// <summary>
        /// Update the status of a property
        /// </summary>
        /// <param name="propertyId">The ID of the property to update</param>
        /// <param name="request">The status update information</param>
        /// <returns>No content if successful</returns>
        /// <response code="204">If the status was successfully updated</response>
        /// <response code="400">If the request is invalid</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="403">If the user is not authorized to update this property</response>
        /// <response code="404">If the property is not found</response>
        [HttpPut("{propertyId}/status")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [Authorize(Policy = "IsSuperMod")]
        public async Task<IActionResult> UpdateStatus(Guid propertyId, [FromBody] UpdateStatusDto request)
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return Unauthorized(ApiResponse<object>.Failure(INVALID_USER_MESSAGE));
            }

            var result = await _propertyService.UpdateStatusAsync(propertyId, userId.Value, request);
            return HandleResult(result);
        }

        /// <summary>
        /// Update the status of multiple properties in bulk
        /// </summary>
        /// <param name="request">The bulk status update information containing property IDs, status, and comment</param>
        /// <returns>No content if successful</returns>
        /// <response code="204">If the statuses were successfully updated</response>
        /// <response code="400">If the request is invalid or some properties couldn't be updated</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="403">If the user is not authorized to update one or more properties</response>
        [HttpPut("bulk/status")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> UpdateStatusBulk([FromBody] BulkUpdateStatusDto request)
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return Unauthorized(ApiResponse<object>.Failure(INVALID_USER_MESSAGE));
            }

            // Create UpdateStatusDto from the bulk request
            var updateStatusDto = new UpdateStatusDto
            {
                Status = request.Status,
                Comment = request.Comment
            };

            // Update status for all properties
            var result = await _propertyService.UpdateStatusBulkAsync(request.PropertyIds, userId.Value, updateStatusDto);
            return HandleResult(result);
        }

        /// <summary>
        /// Search for properties based on various criteria
        /// </summary>
        /// <param name="postType">Filter by post type (e.g., Sell, Rent)</param>
        /// <param name="propertyType">Filter by property type (e.g., Apartment, House)</param>
        /// <param name="status">Filter by property status</param>
        /// <param name="page">Page number for pagination</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <returns>Paged list of properties matching the criteria</returns>
        /// <response code="200">Returns the list of properties</response>
        /// <response code="500">If there was an error processing the request</response>
        [HttpGet("search")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SearchProperties(
            [FromQuery] List<PostType>? postType = null,
            [FromQuery] List<PropertyType>? propertyType = null,
            [FromQuery] List<PropertyStatus>? status = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50)
        {
            var filterCriteria = new PropertyFilterCriteriaDto
            {
                PostTypes = postType,
                PropertyTypes = propertyType,
                Status = status,
                Page = page,
                PageSize = pageSize
            };

            var result = await _propertyService.SearchPropertiesAsync(filterCriteria);

            return HandleResult(result);
        }

        /// <summary>
        /// Get property count statistics by status for all properties
        /// </summary>
        /// <returns>Property count statistics grouped by status</returns>
        /// <response code="200">Returns the property count statistics</response>
        /// <response code="500">If there was an error processing the request</response>
        [HttpGet("count-by-status")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetPropertyCountByStatus()
        {
            var stats = await _propertyService.GetPropertyCountByStatusAsync();
            return HandleResult(stats);
        }

        /// <summary>
        /// Count properties based on search criteria
        /// </summary>
        /// <param name="postType">Filter by post type (e.g., Sell, Rent)</param>
        /// <param name="propertyType">Filter by property type (e.g., Apartment, House)</param>
        /// <param name="status">Filter by property status</param>
        /// <returns>Count of properties matching the criteria</returns>
        /// <response code="200">Returns the count of properties</response>
        /// <response code="500">If there was an error processing the request</response>
        [HttpGet("search/count")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> CountProperties(
            [FromQuery] List<PostType>? postType = null,
            [FromQuery] List<PropertyType>? propertyType = null,
            [FromQuery] List<PropertyStatus>? status = null)
        {
            var filterCriteria = new PropertyFilterCriteriaDto
            {
                PostTypes = postType,
                PropertyTypes = propertyType,
                Status = status
            };

            var count = await _propertyService.CountPropertiesAsync(filterCriteria);

            return HandleResult(count);
        }

        /// <summary>
        /// Get the status history of a property
        /// </summary>
        /// <param name="propertyId">The ID of the property</param>
        /// <returns>List of property status history entries</returns>
        /// <response code="200">Returns the property history</response>
        /// <response code="401">If the user is not authenticated</response>
        [HttpGet("{propertyId}/history")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> GetPropertyHistoryStatus(Guid propertyId)
        {
            var history = await _propertyAnalyticsService.GetPropertyHistoryStatus(propertyId);
            return HandleResult(history);
        }
    }
}
