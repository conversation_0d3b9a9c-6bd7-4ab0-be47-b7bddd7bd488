# Docker Setup for RealEstate System

This document provides instructions for running the RealEstate System using Docker containers.

## Overview

The system consists of:
- **RealEstate.API**: Main public API for the real estate application
- **RealEstate.InternalAPI**: Internal/Admin API for management operations
- **PostgreSQL**: Database server

## Prerequisites

- Docker Desktop installed and running
- Docker Compose v3.8 or higher

## Quick Start

### Development Environment

1. **Clone and navigate to the project directory:**
   ```bash
   cd RealEstateSystem
   ```

2. **Start all services in development mode:**
   ```bash
   docker-compose up -d
   ```

   This will start:
   - PostgreSQL on port 5433
   - RealEstate.API on port 5049
   - RealEstate.InternalAPI on port 5176

3. **View logs:**
   ```bash
   docker-compose logs -f
   ```

4. **Stop all services:**
   ```bash
   docker-compose down
   ```

### Production Environment

1. **Update environment variables in docker-compose.yml:**
   - Change database passwords
   - Update JWT secrets (minimum 32 characters)
   - Configure CORS allowed origins
   - Set up AWS credentials if using S3/SES

2. **Start production services:**
   ```bash
   docker-compose -f docker-compose.yml up -d
   ```

## Configuration

### Environment Variables

#### RealEstate.API
- `ConnectionStrings__DefaultConnection`: PostgreSQL connection string
- `JWT__Key`: JWT signing key (minimum 32 characters)
- `JWT__Issuer`: JWT issuer
- `JWT__Audience`: JWT audience
- `Cors__AllowedOrigins__*`: Allowed CORS origins
- `Storage__Provider`: "Local" or "S3"
- `AWS__S3__*`: S3 configuration (if using S3 storage)
- `AWS__SES__*`: SES configuration (for email)
- `InternalEmail__*`: Internal email addresses

#### RealEstate.InternalAPI
- `ConnectionStrings__YezHomeConnection`: PostgreSQL connection string
- `JWT__Key`: JWT signing key
- `JWT__Issuer`: JWT issuer
- `JWT__Audience`: JWT audience
- `Cors__AllowedOrigins__*`: Allowed CORS origins

### Database Migration

After starting the containers, you may need to run database migrations:

```bash
# Connect to the API container
docker exec -it yezhome-api bash

# Run migrations (if using Entity Framework)
dotnet ef database update
```

## File Storage

### Local Storage (Default)
Files are stored in Docker volumes:
- `api_property_images`: Property images
- `api_user_avatars`: User avatar images
- `api_temp`: Temporary files

### S3 Storage
To use AWS S3, update the environment variables:
```yaml
- Storage__Provider=S3
- AWS__S3__AccessKey=your_access_key
- AWS__S3__SecretKey=your_secret_key
- AWS__S3__Region=us-east-1
- AWS__S3__BucketName=your-bucket-name
```

## Networking

All services communicate through the `yezhome-network` Docker network:
- Services can communicate using container names as hostnames
- External access is available through mapped ports

## Volumes

- `postgres_data`: PostgreSQL data persistence
- `api_property_images`: Property images storage
- `api_user_avatars`: User avatars storage
- `api_temp`: Temporary files storage

## Troubleshooting

### Common Issues

1. **Port conflicts:**
   - Change port mappings in docker-compose.yml if ports are already in use

2. **Database connection issues:**
   - Ensure PostgreSQL container is running: `docker-compose ps`
   - Check connection strings match database configuration

3. **Build failures:**
   - Clean Docker cache: `docker system prune -a`
   - Rebuild images: `docker-compose build --no-cache`

### Useful Commands

```bash
# View running containers
docker-compose ps

# View logs for specific service
docker-compose logs yezhome-api

# Restart specific service
docker-compose restart yezhome-api

# Execute command in container
docker exec -it yezhome-api bash

# Remove all containers and volumes
docker-compose down -v
```

## Security Notes

- Change default passwords in production
- Use strong JWT secrets (minimum 32 characters)
- Configure proper CORS origins
- Use environment files for sensitive data
- Consider using Docker secrets for production deployments

## API Endpoints

- **RealEstate.API**: http://localhost:5049
  - Swagger UI: http://localhost:5049/swagger
- **RealEstate.InternalAPI**: http://localhost:5176
  - Swagger UI: http://localhost:5176/swagger
