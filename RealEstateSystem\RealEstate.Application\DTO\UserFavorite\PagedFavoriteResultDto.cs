namespace RealEstate.Application.DTO.UserFavorite
{
    public class PagedFavoriteResultDto
    {
        public required IEnumerable<FavoriteWithPropertyDto> Items { get; set; }
        public int TotalCount { get; set; }
        public int CurrentPage { get; set; }
        public int PageSize { get; set; }
        public int PageCount { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
    }
}
