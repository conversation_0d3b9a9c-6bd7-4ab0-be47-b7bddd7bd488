﻿using AutoMapper;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using RealEstate.Domain.CustomModel;
using Shared.Constants;
using Shared.Enums;
using Shared.Results;
using Shared.Extensions;

namespace RealEstate.Application.Services
{
    public class BlogService : IBlogService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public BlogService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<Result<BlogPostDto>> GetBlogByIdAsync(Guid id)
        {
            var blogPost = await _unitOfWork.BlogPosts.GetByIdAsync(id);
            if (blogPost == null)
                return Result<BlogPostDto>.Failure(BlogPostMessage.BlogPostNotFound, ErrorType.NotFound);
            return Result<BlogPostDto>.Success(_mapper.Map<BlogPostDto>(blogPost));
        }

        public async Task<Result<BlogPostDto>> GetBlogBySlugAsync(string slug)
        {
            var blogPost = await _unitOfWork.BlogPosts.GetBlogPostBySlugAsync(slug);
            if (blogPost == null)
                return Result<BlogPostDto>.Failure(BlogPostMessage.BlogPostNotFound, ErrorType.NotFound);
            return Result<BlogPostDto>.Success(_mapper.Map<BlogPostDto>(blogPost));
        }

        public async Task<Result<IEnumerable<BlogPostDto>>> GetAllBlogAsync()
        {
            var blogPosts = await _unitOfWork.BlogPosts.GetAllAsync();
            return Result<IEnumerable<BlogPostDto>>.Success(_mapper.Map<IEnumerable<BlogPostDto>>(blogPosts));
        }

        public async Task<Result<PagedResult<BlogPostDto>>> GetBlogAsync(PagingRequest paging, string? title)
        {
            var blogs = await _unitOfWork.BlogPosts.GetPagedAsync(
                paging,
                p => string.IsNullOrEmpty(title) || p.Title.ToLower().Contains(title.ToLower())
            );

            var dtoList = _mapper.Map<IEnumerable<BlogPostDto>>(blogs.Items);

            var pagedResult = new PagedResult<BlogPostDto>
            {
                Items = dtoList,
                TotalCount = blogs.TotalCount
            };

            return Result<PagedResult<BlogPostDto>>.Success(pagedResult);
        }

        public async Task<Result<BlogPostDto>> CreateBlogAsync(CreateBlogPostDto blogPostDto, Guid userId)
        {
            var blogPost = _mapper.Map<BlogPost>(blogPostDto);

            blogPost.Slug = blogPostDto.Title.ToSlug();
            blogPost.AuthorID = userId;
            blogPost.CreatedBy = userId;
            blogPost.CreatedAt = DateTime.UtcNow;
            await _unitOfWork.BlogPosts.AddAsync(blogPost);
            await _unitOfWork.SaveChangesAsync();
            return Result<BlogPostDto>.Success(_mapper.Map<BlogPostDto>(blogPost));
        }

        public async Task<Result> UpdateBlogAsync(Guid id, CreateBlogPostDto blogPostDto, Guid userId)
        {
            var blogPost = await _unitOfWork.BlogPosts.GetByIdAsync(id);
            if (blogPost == null) return Result.Failure(BlogPostMessage.BlogPostNotFound, ErrorType.NotFound);
            _mapper.Map(blogPostDto, blogPost);

            blogPost.UpdatedBy = userId;
            _unitOfWork.BlogPosts.Update(blogPost);
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public async Task<Result> DeleteBlogAsync(Guid id, Guid userId)
        {
            var blogPost = await _unitOfWork.BlogPosts.GetByIdAsync(id);
            if (blogPost == null) return Result.Failure(BlogPostMessage.BlogPostNotFound, ErrorType.NotFound);

            blogPost.UpdatedBy = userId;
            _unitOfWork.BlogPosts.Remove(blogPost);
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }
    }
}
