using System.ComponentModel.DataAnnotations;

namespace RealEstate.Application.DTO
{
    public class UserInvoiceInfoDto
    {
        [Required]
        public string? BuyerName { get; set; }

        [Required]
        [EmailAddress]
        public string? Email { get; set; }

        [Required]
        public string? CompanyName { get; set; }

        [Required]
        [RegularExpression(@"^(\d{10}|\d{10}-\d{3})$", ErrorMessage = "Tax Code must be 10 digits or 10 digits followed by a hyphen and 3 digits")]
        public string? TaxCode { get; set; }

        [Required]
        public string? Address { get; set; }

        public string GetNormalizedEmail() => Email?.Trim().ToLowerInvariant();
    }
}
