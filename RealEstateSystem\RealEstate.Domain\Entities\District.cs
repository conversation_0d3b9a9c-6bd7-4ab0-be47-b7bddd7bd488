using RealEstate.Domain.Interfaces;

namespace RealEstate.Domain.Entities
{
    public class District
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Slug { get; set; }
        public string Type { get; set; }
        public string NameWithType { get; set; }
        public string Path { get; set; }
        public string PathWithType { get; set; }
        public int CityId { get; set; }
        
        public City City { get; set; }
        public ICollection<Ward> Wards { get; set; } = new List<Ward>();
        public ICollection<Street> Streets { get; set; } = new List<Street>();
    }
} 