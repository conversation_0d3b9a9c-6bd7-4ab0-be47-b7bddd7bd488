﻿using RealEstate.Domain.Entities;
using Shared.Results;

namespace RealEstate.Application.Interfaces
{
    public interface IPasswordResetService
    {
        Task<Result<string>> GenerateAndStoreResetToken(Guid userId);
        Task<Result<(bool isValid, Guid userId)>> ValidateResetToken(string token);
        Task<Result> MarkTokenAsUsed(string token);
        Task<Result<PasswordResetToken>> GetTokenByValue(string token);
    }
}