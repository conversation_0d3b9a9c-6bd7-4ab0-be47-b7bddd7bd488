using System;

namespace RealEstate.Application.DTO
{
    public class CreateUserAvatarDto
    {
        public Guid Id { get; set; }
        public Guid UserID { get; set; }
        public string? MediaType { get; set; }
        public string? MediaURL { get; set; }
        public string? FilePath { get; set; }
        public string? ThumbnailPath { get; set; }
        public string? SmallPath { get; set; }
        public string? MediumPath { get; set; }
        public string? LargePath { get; set; }
        public DateTime UploadedAt { get; set; }
        public string? Caption { get; set; }
    }
}
