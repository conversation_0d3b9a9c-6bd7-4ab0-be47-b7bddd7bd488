﻿using PhoneNumbers;

namespace Shared.Helper
{
    public static class PhoneUtils
    {
        private static readonly PhoneNumberUtil _phoneUtil = PhoneNumberUtil.GetInstance();

        public static string? NormalizePhoneNumber(string input, string region = "VN")
        {
            try
            {
                var number = _phoneUtil.Parse(input, region);
                if (_phoneUtil.IsValidNumber(number))
                    return _phoneUtil.Format(number, PhoneNumberFormat.E164);
            }
            catch { }
            return null;
        }
    }
}
