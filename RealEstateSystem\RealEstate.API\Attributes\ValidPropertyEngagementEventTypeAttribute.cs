using System.ComponentModel.DataAnnotations;
using RealEstate.Domain.Common;

namespace RealEstate.API.Attributes
{
    public class ValidPropertyEngagementEventTypeAttribute : ValidationAttribute
    {
        public override bool IsValid(object? value)
        {
            if (value == null) return true; // Let RequiredAttribute handle null values
            
            if (value is string eventType)
            {
                return FunctionHelper.IsValidPropertyEngagementEventType(eventType);
            }
            
            return false;
        }

        public override string FormatErrorMessage(string name)
        {
            return $"The {name} field must be a valid event type (view, favorite, click_phone, chat, contact, share, search_impression, click_through, conversion, print, save_for_later, report_listing, view_map, view_gallery, view_floor_plan, view_video).";
        }
    }
} 