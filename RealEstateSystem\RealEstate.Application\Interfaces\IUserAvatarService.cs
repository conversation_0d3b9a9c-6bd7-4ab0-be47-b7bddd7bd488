using RealEstate.Application.DTO;
using System;
using System.Threading.Tasks;

namespace RealEstate.Application.Interfaces
{
    public interface IUserAvatarService
    {
        Task<UserAvatarDto> GetUserAvatarByIdAsync(Guid id);
        Task<UserAvatarDto> GetUserAvatarByUserIdAsync(Guid userId);
        Task<UserAvatarDto> CreateUserAvatarAsync(CreateUserAvatarDto userAvatarDto);
        Task<UserAvatarDto> UpdateUserAvatarAsync(Guid id, CreateUserAvatarDto userAvatarDto);
        Task<bool> DeleteUserAvatarAsync(Guid id);
    }
}
