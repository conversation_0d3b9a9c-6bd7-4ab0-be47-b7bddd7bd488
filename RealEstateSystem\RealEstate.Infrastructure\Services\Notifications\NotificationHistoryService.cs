﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Notification;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Common;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using Shared.Enums;
using Shared.Results;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.Infrastructure.Services.Notifications
{
    public class NotificationHistoryService : INotificationHistoryService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly INotificationPreferenceService _notificationPreferenceService;


        public NotificationHistoryService(IUnitOfWork unitOfWork, IMapper mapper, INotificationPreferenceService notificationPreferenceService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _notificationPreferenceService = notificationPreferenceService;
        }

        public async Task<Result<NotificationDto>> GetNotificationByIdAsync(Guid id)
        {
            var notification = await _unitOfWork.Notifications.GetByIdAsync(id);
            if (notification == null)
            {
                return Result<NotificationDto>.Failure("Notification not found.", ErrorType.NotFound);
            }
            return Result<NotificationDto>.Success(_mapper.Map<NotificationDto>(notification));
        }

        public async Task<Result<PagedResultDto<NotificationDto>>> GetNotificationsForUserAsync(
            Guid userId,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            int page = 1,
            int pageSize = 50)
        {
            var query = _unitOfWork.Notifications.GetQueryable()
                .Where(n => n.UserId == userId
                        || n.Category == EnumValues.NotificationCategory.Miscellaneous
                        || n.Category == EnumValues.NotificationCategory.Promotion
                        );

            // Apply date filters
            query = ApplyDateFilters(query, fromDate, toDate);

            // Get total count for pagination
            var totalCount = await query.CountAsync();

            // Apply pagination
            var notifications = await query
                .OrderByDescending(n => n.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .AsNoTracking()
                .ToListAsync();

            // Map to DTOs
            var notificationDtos = _mapper.Map<List<NotificationDto>>(notifications);

            // Return paged result
            var result = new PagedResultDto<NotificationDto>
            {
                Items = notificationDtos,
                TotalCount = totalCount,
                PageCount = (int)Math.Ceiling(totalCount / (double)pageSize),
                CurrentPage = page,
                PageSize = pageSize
            };
            return Result<PagedResultDto<NotificationDto>>.Success(result);
        }

        public async Task<Result<PagedResultDto<NotificationDto>>> GetNotificationsByCategoryAsync(
            string? category = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            int page = 1,
            int pageSize = 50,
            bool getOnlySystem = true)
        {
            var query = _unitOfWork.Notifications.GetQueryable();

            if (!string.IsNullOrEmpty(category))
            {
                if (Enum.TryParse<NotificationCategory>(category, true, out var categoryEnum))
                {
                    query = query.Where(n => n.Category == categoryEnum);
                }
                else
                {
                    var emptyResult = new PagedResultDto<NotificationDto> { Items = new List<NotificationDto>() };
                    return Result<PagedResultDto<NotificationDto>>.Success(emptyResult);
                }
            }

            // Apply date filters
            query = ApplyDateFilters(query, fromDate, toDate);

            // Get total count for pagination
            var totalCount = await query.CountAsync();

            // Apply pagination
            var notifications = await query
                .OrderByDescending(n => n.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .AsNoTracking()
                .ToListAsync();

            // Map to DTOs
            var notificationDtos = _mapper.Map<List<NotificationDto>>(notifications);

            // Return paged result
            var result = new PagedResultDto<NotificationDto>
            {
                Items = notificationDtos,
                TotalCount = totalCount,
                PageCount = (int)Math.Ceiling(totalCount / (double)pageSize),
                CurrentPage = page,
                PageSize = pageSize
            };
            return Result<PagedResultDto<NotificationDto>>.Success(result);
        }

        public async Task<Result<PagedResultDto<NotificationDto>>> GetNotificationsForUserAsync(
            Guid userId,
            string? category = null, // Nhận chuỗi từ front-end
            bool? unreadOnly = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            int page = 1,
            int pageSize = 50)
        {
            var query = _unitOfWork.Notifications.GetQueryable();

            // 1. LỌC THEO USER: Luôn là bước đầu tiên và quan trọng nhất.
            // Lấy thông báo có userId là của user này HOẶC thông báo toàn hệ thống (userId là null).
            query = query.Where(n => n.UserId == userId || n.UserId == null);

            // 2. LỌC THEO CATEGORY (một cách an toàn)
            if (!string.IsNullOrEmpty(category))
            {
                // Chuyển đổi chuỗi category từ front-end thành enum.
                // Tham số 'ignoreCase: true' giúp chấp nhận cả "listing" và "Listing".
                if (Enum.TryParse<NotificationCategory>(category, true, out var categoryEnum))
                {
                    // Nếu chuyển đổi thành công, áp dụng bộ lọc.
                    query = query.Where(n => n.Category == categoryEnum);
                }
                else
                {
                    // Nếu front-end gửi lên một category không hợp lệ, trả về danh sách rỗng.
                    var emptyResult = new PagedResultDto<NotificationDto> { Items = new List<NotificationDto>() };
                    return Result<PagedResultDto<NotificationDto>>.Success(emptyResult);
                }
            }
            // Nếu 'category' là null, ta không lọc gì cả, tương ứng với tab "Tất cả".

            // 3. LỌC THEO TRẠNG THÁI "CHƯA ĐỌC" (nếu có yêu cầu)
            if (unreadOnly == true)
            {
                // Giả sử bạn có trường IsRead trong Notification entity
                query = query.Where(n => !n.IsRead);
            }

            // --- Các phần còn lại không đổi ---

            // 4. Áp dụng bộ lọc ngày (nếu có)
            if (fromDate.HasValue)
            {
                query = query.Where(n => n.CreatedAt >= fromDate.Value);
            }
            if (toDate.HasValue)
            {
                query = query.Where(n => n.CreatedAt <= toDate.Value);
            }

            // 5. Phân trang và trả về kết quả
            var totalCount = await query.CountAsync();

            var notifications = await query
                .OrderByDescending(n => n.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .AsNoTracking()
                .ToListAsync();

            var notificationDtos = _mapper.Map<List<NotificationDto>>(notifications);

            var result = new PagedResultDto<NotificationDto>
            {
                Items = notificationDtos,
                TotalCount = totalCount,
                PageCount = (int)Math.Ceiling(totalCount / (double)pageSize),
                CurrentPage = page,
                PageSize = pageSize
            };

            return Result<PagedResultDto<NotificationDto>>.Success(result);
        }

        public async Task<Result<int>> GetUnreadNotificationCountAsync(Guid userId)
        {
            var count = await _unitOfWork.Notifications.GetQueryable()
                .CountAsync(n => n.UserId == userId && !n.IsRead ||
                                n.UserId == null && !n.IsRead && (n.Category == EnumValues.NotificationCategory.Miscellaneous
                                || n.Category == EnumValues.NotificationCategory.Promotion));
            return Result<int>.Success(count);
        }

        public async Task<Result> MarkAllAsReadAsync(Guid userId)
        {
            var notifications = await _unitOfWork.Notifications.GetQueryable()
                .Where(n => n.UserId == userId && !n.IsRead)
                .ToListAsync();

            if (!notifications.Any())
            {
                return Result.Success(); // Nothing to update
            }

            foreach (var notification in notifications)
            {
                notification.IsRead = true;
            }

            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public async Task<Result> MarkAsReadAsync(Guid id, Guid userId)
        {
            var notification = await _unitOfWork.Notifications.GetByIdAsync(id, asNoTracking: false);

            if (notification == null)
                return Result.Failure("Notification not found.", ErrorType.NotFound);

            // Verify the notification belongs to this user or is a system/promotion notification
            if (notification.UserId.HasValue && notification.UserId.Value != userId)
                return Result.Failure("Unauthorized to mark this notification as read.", ErrorType.Unauthorized);

            notification.IsRead = true;
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public async Task<Result> MarkMultipleAsReadAsync(List<Guid> notificationIds, Guid userId)
        {
            if (notificationIds == null || !notificationIds.Any())
            {
                return Result.Failure("No notification IDs provided", ErrorType.Validation);
            }

            var notifications = await _unitOfWork.Notifications.GetQueryable()
                .Where(n => notificationIds.Contains(n.Id) && n.UserId == userId)
                .ToListAsync();

            foreach (var notification in notifications)
            {
                notification.IsRead = true;
            }

            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public async Task<Result> DeleteNotificationAsync(Guid id, Guid userId)
        {
            var notification = await _unitOfWork.Notifications.GetByIdAsync(id);

            if (notification == null)
                return Result.Failure("Notification not found.", ErrorType.NotFound);

            // Only allow deletion if the notification belongs to this user
            if (notification.UserId.HasValue && notification.UserId.Value != userId)
                return Result.Failure("Unauthorized to delete this notification.", ErrorType.Unauthorized);

            _unitOfWork.Notifications.Remove(notification);
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }
        
        public async Task<Result<NotificationDto>> CreateNotificationAsync(CreateNotificationDto notificationDto)
        {
            var notification = _mapper.Map<Notification>(notificationDto);
            await _unitOfWork.Notifications.AddAsync(notification);
            await _unitOfWork.SaveChangesAsync();
            var resultDto = _mapper.Map<NotificationDto>(notification);
            return Result<NotificationDto>.Success(resultDto);
        }

        private IQueryable<Notification> ApplyDateFilters(
            IQueryable<Notification> query,
            DateTime? fromDate,
            DateTime? toDate)
        {
            if (fromDate.HasValue)
            {
                query = query.Where(n => n.CreatedAt >= fromDate.Value);
            }

            if (toDate.HasValue)
            {
                query = query.Where(n => n.CreatedAt <= toDate.Value);
            }

            return query;
        }
    }
}
