﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RealEstate.Domain.Entities;
using RealEstate.Infrastructure;
using Microsoft.Extensions.Caching.Memory;

namespace RealEstate.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AddressController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly IMemoryCache _memoryCache;

        public AddressController(ApplicationDbContext context, ILogger<AddressController> logger, IMemoryCache memoryCache)
        {
            _context = context;
            _memoryCache = memoryCache;
        }

        [HttpGet("cities")]
        public async Task<ActionResult<IEnumerable<City>>> GetCitiesAsync()
        {
            try
            {
                const string cacheKey = "CitiesList";
                if (_memoryCache.TryGetValue(cacheKey, out IEnumerable<City> cities))
                {
                    return Ok(cities);
                }

                cities = await _context.City.OrderBy(x => x.Id).ToListAsync();
                if (cities == null || !cities.Any())
                {
                    return NotFound(new { Message = "No cities found" });
                }

                var cacheEntryOptions = new MemoryCacheEntryOptions()
                    .SetSlidingExpiration(TimeSpan.FromMinutes(30))
                    .SetAbsoluteExpiration(TimeSpan.FromHours(1));

                _memoryCache.Set(cacheKey, cities, cacheEntryOptions);

                return Ok(cities);
            }
            catch (Exception)
            {
                return StatusCode(500, new { Message = "An error occurred while retrieving cities. Please try again later." });
            }
        }

        [HttpGet("cities/{cityId}/districts")]
        public async Task<ActionResult<IEnumerable<District>>> GetDistrictByCityAsync(int cityId)
        {
            try
            {
                string cacheKey = $"DistrictsForCity_{cityId}";
                if (_memoryCache.TryGetValue(cacheKey, out IEnumerable<District> districts))
                {
                    return Ok(districts);
                }

                districts = await _context.District.Where(d => d.CityId == cityId).OrderBy(x => x.NameWithType).ToListAsync();
                if (districts == null || !districts.Any())
                {
                    return NotFound(new { Message = "No districts found for the specified city" });
                }

                var cacheEntryOptions = new MemoryCacheEntryOptions()
                    .SetSlidingExpiration(TimeSpan.FromMinutes(30))
                    .SetAbsoluteExpiration(TimeSpan.FromHours(1));

                _memoryCache.Set(cacheKey, districts, cacheEntryOptions);

                return Ok(districts);
            }
            catch (Exception)
            {
                return StatusCode(500, new { Message = "An error occurred while retrieving districts. Please try again later." });
            }
        }

        [HttpGet("districts/{districtId}/wards")]
        public async Task<ActionResult<IEnumerable<Ward>>> GetWardByDistrictAsync(int districtId)
        {
            try
            {
                string cacheKey = $"WardsForDistrict_{districtId}";
                if (_memoryCache.TryGetValue(cacheKey, out IEnumerable<Ward> wards))
                {
                    return Ok(wards);
                }

                wards = await _context.Ward.Where(d => d.DistrictId == districtId).OrderBy(x => x.NameWithType).ToListAsync();
                if (wards == null || !wards.Any())
                {
                    return NotFound(new { Message = "No wards found for the specified district" });
                }

                var cacheEntryOptions = new MemoryCacheEntryOptions()
                    .SetSlidingExpiration(TimeSpan.FromMinutes(30))
                    .SetAbsoluteExpiration(TimeSpan.FromHours(1));

                _memoryCache.Set(cacheKey, wards, cacheEntryOptions);

                return Ok(wards);
            }
            catch (Exception)
            {
                return StatusCode(500, new { Message = "An error occurred while retrieving wards. Please try again later." });
            }
        }

        [HttpGet("districts/{districtId}/streets")]
        public async Task<ActionResult<IEnumerable<Street>>> GetStreetByDistrictAsync(int districtId)
        {
            try
            {
                string cacheKey = $"StreetsForDistrict_{districtId}";
                if (_memoryCache.TryGetValue(cacheKey, out IEnumerable<Street> streets))
                {
                    return Ok(streets);
                }

                streets = await _context.Street.Where(s => s.DistrictId == districtId).ToListAsync();
                if (streets == null || !streets.Any())
                {
                    return NotFound(new { Message = "No streets found for the specified district" });
                }

                var cacheEntryOptions = new MemoryCacheEntryOptions()
                    .SetSlidingExpiration(TimeSpan.FromMinutes(30))
                    .SetAbsoluteExpiration(TimeSpan.FromHours(1));

                _memoryCache.Set(cacheKey, streets, cacheEntryOptions);

                return Ok(streets);
            }
            catch (Exception)
            {
                return StatusCode(500, new { Message = "An error occurred while retrieving streets. Please try again later." });
            }
        }

        [HttpGet("wards/{wardId}/streets/{streetId}/projects")]
        public async Task<ActionResult<IEnumerable<Project>>> GetProjectsByWardStreetAsync(int wardId, int streetId)
        {
            try
            {
                string cacheKey = $"ProjectsForWard_{wardId}_Street_{streetId}";
                if (_memoryCache.TryGetValue(cacheKey, out IEnumerable<Project> projects))
                {
                    return Ok(projects);
                }

                projects = await _context.Project.Where(p => p.WardId == wardId && p.StreetId == streetId).ToListAsync();
                if (projects == null || !projects.Any())
                {
                    return NotFound(new { Message = "No projects found for the specified ward and street." });
                }

                var cacheEntryOptions = new MemoryCacheEntryOptions()
                    .SetSlidingExpiration(TimeSpan.FromMinutes(30))
                    .SetAbsoluteExpiration(TimeSpan.FromHours(1));

                _memoryCache.Set(cacheKey, projects, cacheEntryOptions);

                return Ok(projects);
            }
            catch (Exception)
            {
                return StatusCode(500, new { Message = "An error occurred while retrieving projects. Please try again later." });
            }
        }
    }
}
