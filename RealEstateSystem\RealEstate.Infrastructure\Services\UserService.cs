﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Common;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using Shared.Enums;
using Shared.Helper;
using Shared.Results;

namespace RealEstate.Application.Services
{
    public class UserService : IUserService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IUserAvatarService _userAvatarService;
        private readonly IYezDataService _yezDataService;

        public UserService(IUnitOfWork unitOfWork, IMapper mapper, IUserAvatarService userAvatarService, IYezDataService yezDataService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _userAvatarService = userAvatarService;
            _yezDataService = yezDataService;
        }

        public async Task<Result<ProfileDto>> GetUserProfileAsync(Guid userId)
        {
            var userResult = await GetUserByIdAsync(userId);
            if (!userResult.IsSuccess)
            {
                return Result<ProfileDto>.Failure($"Không tìm thấy người dùng với ID = {userId}.", ErrorType.NotFound);
            }
            var user = userResult.Value;

            var userAvatar = await _userAvatarService.GetUserAvatarByUserIdAsync(userId);
            var memberRankDetails = await _yezDataService.GetMemberRankingByNameAsync(user.MemberRank);
            var highlightFee = await _yezDataService.GetHighlightFeeByRankAsync(user.MemberRank);

            if (userAvatar != null)
            {
                user.AvatarURL = userAvatar.MediaURL;
            }

            var profileDto = new ProfileDto
            {
                User = user,
                MemberRankDetails = memberRankDetails,
                HighlightFee = highlightFee
            };

            return Result<ProfileDto>.Success(profileDto);
        }

        public async Task<Result<UserDto>> GetUserByIdAsync(Guid id, bool isIncludeWalletInfo = true)
        {
            var user = await _unitOfWork.AppUsers.GetQueryable()
                .Where(u => u.Id == id)
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .ThenInclude(r => r.RolePermissions)
                .ThenInclude(rp => rp.Permission)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            if (user == null)
                return Result<UserDto>.Failure("User not found.", ErrorType.NotFound);

            var userDto = _mapper.Map<UserDto>(user);

            if (isIncludeWalletInfo)
            {
                var wallet = await _unitOfWork.Wallets.GetQueryable().FirstOrDefaultAsync(w => w.UserId == id);
                if (wallet == null)
                {
                    wallet = new Wallet { UserId = id, Balance = 0 };
                    await _unitOfWork.Wallets.AddAsync(wallet);
                    await _unitOfWork.SaveChangesAsync();
                }
                userDto.Wallet = _mapper.Map<WalletDto>(wallet);
            }

            return Result<UserDto>.Success(userDto);
        }

        public async Task<Result<UserDto>> GetUserByEmailAsync(string email)
        {
            var user = await _unitOfWork.AppUsers.GetByEmailAsync(email);
            if (user == null) return Result<UserDto>.Failure("User not found", ErrorType.NotFound);
            return Result<UserDto>.Success(_mapper.Map<UserDto>(user));
        }

        public async Task<Result<IEnumerable<UserDto>>> GetAllUsersAsync()
        {
            var users = await _unitOfWork.AppUsers.GetAllAsync();
            return Result<IEnumerable<UserDto>>.Success(_mapper.Map<IEnumerable<UserDto>>(users));
        }

        public async Task<Result<UserDto>> CreateUserAsync(CreateUserDto userDto)
        {
            var user = _mapper.Map<AppUser>(userDto);
            await _unitOfWork.AppUsers.AddAsync(user);
            await _unitOfWork.SaveChangesAsync();
            return Result<UserDto>.Success(_mapper.Map<UserDto>(user));
        }

        public async Task<Result> UpdateUserAsync(Guid id, CreateUserDto userDto)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(id);
            if (user == null) return Result.Failure("User not found", ErrorType.NotFound);
            _mapper.Map(userDto, user);
            _unitOfWork.AppUsers.Update(user);
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public async Task<Result> DeleteUserAsync(Guid id)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(id);
            if (user == null) return Result.Failure("User not found", ErrorType.NotFound);
            _unitOfWork.AppUsers.Remove(user);
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public async Task<Result> AddUserRoleAsync(AddUserRoleDto addUserRoleDto)
        {
            var userRole = _mapper.Map<UserRole>(addUserRoleDto);
            await _unitOfWork.UserRoles.AddAsync(userRole);
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public async Task<Result> UpdateUserAvatarAsync(Guid userId, string avatarImage)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return Result.Failure("User not found", ErrorType.NotFound);

            user.AvatarImage = avatarImage;
            _unitOfWork.AppUsers.Update(user);
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public async Task<Result<string>> GetUserAvatarImageAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return Result<string>.Failure("User not found", ErrorType.NotFound);
            return Result<string>.Success(user.AvatarImage);
        }

        public async Task<Result> DeactivateUserAsync(Guid userId, DeactivateUserDto deactivateUserDto)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return Result.Failure("User not found", ErrorType.NotFound);

            // Password check logic here...

            user.IsActive = false;
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public async Task<Result> ReactivateUserAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return Result.Failure("User not found", ErrorType.NotFound);
            user.IsActive = true;
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public async Task<Result> PermanentDeleteUserAsync(Guid userId, DeactivateUserDto deactivateUserDto)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return Result.Failure("User not found", ErrorType.NotFound);

            // Password check logic here...

            _unitOfWork.AppUsers.Remove(user);
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public async Task<Result> UpdateUserTaxInfoAsync(Guid userId, UserTaxInfoDto taxInfoDto)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return Result.Failure("User not found", ErrorType.NotFound);

            user.PersonalTaxCode = taxInfoDto.PersonalTaxCode;
            user.InvoiceBuyerName = taxInfoDto.InvoiceInfo?.BuyerName;
            user.InvoiceEmail = taxInfoDto.InvoiceInfo?.GetNormalizedEmail();
            user.InvoiceCompanyName = taxInfoDto.InvoiceInfo?.CompanyName;
            user.InvoiceTaxCode = taxInfoDto.InvoiceInfo?.TaxCode;
            user.InvoiceAddress = taxInfoDto.InvoiceInfo?.Address;

            _unitOfWork.AppUsers.Update(user);
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public async Task<Result<UserTaxInfoDto>> GetUserInvoiceInfoAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return Result<UserTaxInfoDto>.Failure("User not found", ErrorType.NotFound);

            var info = _mapper.Map<UserTaxInfoDto>(user);
            return Result<UserTaxInfoDto>.Success(info);
        }

        public async Task<Result<bool>> IsUserExistsAsync(Guid userId)
        {
            var exists = await _unitOfWork.AppUsers.GetQueryable().AnyAsync(u => u.Id == userId);
            return Result<bool>.Success(exists);
        }

        public async Task<Result<IEnumerable<PermissionDto>>> GetUserPermissionsAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetQueryable().Include(u => u.UserRoles).ThenInclude(ur => ur.Role).ThenInclude(r => r.RolePermissions).ThenInclude(rp => rp.Permission).FirstOrDefaultAsync(u => u.Id == userId);
            if (user == null) return Result<IEnumerable<PermissionDto>>.Failure("User not found", ErrorType.NotFound);

            var permissions = user.UserRoles.SelectMany(ur => ur.Role.RolePermissions).Select(rp => rp.Permission).Distinct();
            return Result<IEnumerable<PermissionDto>>.Success(_mapper.Map<IEnumerable<PermissionDto>>(permissions));
        }

        public async Task<Result<bool>> UserHasPermissionAsync(Guid userId, string permissionCode)
        {
            var user = await _unitOfWork.AppUsers.GetQueryable().Include(u => u.UserRoles).ThenInclude(ur => ur.Role).ThenInclude(r => r.RolePermissions).ThenInclude(rp => rp.Permission).FirstOrDefaultAsync(u => u.Id == userId);
            if (user == null) return Result<bool>.Failure("User not found", ErrorType.NotFound);

            var hasPermission = user.UserRoles.SelectMany(ur => ur.Role.RolePermissions).Any(rp => rp.Permission.Code == permissionCode);
            return Result<bool>.Success(hasPermission);
        }

        public async Task<Result<IEnumerable<AdminRoleDto>>> GetUserRolesAsync(Guid userId)
        {
            var user = await _unitOfWork.AppUsers.GetQueryable().Include(u => u.UserRoles).ThenInclude(ur => ur.Role).FirstOrDefaultAsync(u => u.Id == userId);
            if (user == null) return Result<IEnumerable<AdminRoleDto>>.Failure("User not found", ErrorType.NotFound);

            var roles = user.UserRoles.Select(ur => ur.Role);
            return Result<IEnumerable<AdminRoleDto>>.Success(_mapper.Map<IEnumerable<AdminRoleDto>>(roles));
        }

        public async Task<Result<bool>> IsUserAdminExistsAsync(Guid userId)
        {
            var exists = await _unitOfWork.AppUsers.GetQueryable().AnyAsync(u => u.Id == userId && u.UserType == EnumValues.UserType.Admin);
            return Result<bool>.Success(exists);
        }

        public async Task<Result<bool>> UpdateUserPassword(Guid userId, string password, string token)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return Result<bool>.Failure("User not found", ErrorType.NotFound);

            // Generate salt and hash password
            var salt = GenerateRandomSalt();
            var hashedPassword = HashPassword(password, salt);
            user.PasswordHash = hashedPassword;
            user.PasswordSalt = salt;

            _unitOfWork.AppUsers.Update(user);

            // Mark token as used
            var resetToken = await _unitOfWork.PasswordResetTokens.FindOneAsync(t => t.Token == token);
            if (resetToken == null)
            {
                return Result<bool>.Failure("Token not found", ErrorType.NotFound);
            }

            resetToken.IsUsed = true;
            _unitOfWork.PasswordResetTokens.Update(resetToken);

            await _unitOfWork.SaveChangesAsync();
            return Result<bool>.Success(true);
        }

        #region User Management Methods for Admin

        public async Task<Result<PagedResultDto<UserDto>>> GetNonAdminUsersAsync(UserFilterDto filter)
        {
            var query = _unitOfWork.AppUsers.GetQueryable()
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .Where(u => u.UserType != EnumValues.UserType.Admin && !u.IsDeleted);

            // Apply filters
            query = ApplyUserFilters(query, filter);

            // Get total count
            var totalCount = await query.CountAsync();

            // Apply sorting
            query = ApplyUserSorting(query, filter);

            // Apply pagination
            var users = await query
                .Skip((filter.PageNumber - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            // Map to DTOs
            var userDtos = new List<UserDto>();
            foreach (var user in users)
            {
                var userDto = _mapper.Map<UserDto>(user);

                // Get wallet information
                var wallet = await _unitOfWork.Wallets.GetQueryable()
                    .FirstOrDefaultAsync(w => w.UserId == user.Id);
                userDto.Wallet = wallet != null ? _mapper.Map<WalletDto>(wallet) : null;

                // Map invoice information
                userDto.PersonalTaxCode = user.PersonalTaxCode;
                userDto.InvoiceInfo = new UserInvoiceInfoDto
                {
                    BuyerName = user.InvoiceBuyerName,
                    Email = user.InvoiceEmail,
                    CompanyName = user.InvoiceCompanyName,
                    TaxCode = user.InvoiceTaxCode,
                    Address = user.InvoiceAddress
                };

                userDtos.Add(userDto);
            }

            var pageCount = (int)Math.Ceiling(totalCount / (double)filter.PageSize);

            var result = new PagedResultDto<UserDto>
            {
                Items = userDtos,
                TotalCount = totalCount,
                PageCount = pageCount,
                CurrentPage = filter.PageNumber,
                PageSize = filter.PageSize
            };
            return Result<PagedResultDto<UserDto>>.Success(result);
        }

        public async Task<Result<PagedResultDto<UserDto>>> GetAdminUsersAsync(UserFilterDto filter)
        {
            var query = _unitOfWork.AppUsers.GetQueryable()
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .ThenInclude(r => r.RolePermissions)
                .ThenInclude(rp => rp.Permission)
                .Where(u => u.UserType == EnumValues.UserType.Admin && !u.IsDeleted);

            // Apply filters
            query = ApplyUserFilters(query, filter);

            // Get total count
            var totalCount = await query.CountAsync();

            // Apply sorting
            query = ApplyUserSorting(query, filter);

            // Apply pagination
            var users = await query
                .Skip((filter.PageNumber - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            // Map to DTOs
            var userDtos = new List<UserDto>();
            foreach (var user in users)
            {
                var userDto = _mapper.Map<UserDto>(user);

                // Include role and permission data for admin users
                var roleCodes = user.UserRoles?.Select(ur => ur.Role.Code).ToList() ?? new List<string>();
                userDto.Roles = roleCodes;

                var permissionCodes = user.UserRoles?
                    .SelectMany(ur => ur.Role.RolePermissions ?? new List<RolePermission>())
                    .Select(rp => rp.Permission.Code)
                    .Distinct()
                    .ToList() ?? new List<string>();
                userDto.Permissions = permissionCodes;

                // Get role objects (new detailed objects)
                var roles = user.UserRoles?.Select(ur => ur.Role).ToList() ?? new List<AdminRole>();
                userDto.RoleObjects = _mapper.Map<IEnumerable<AdminRoleDto>>(roles);

                // Get permission objects (new detailed objects)
                var permissions = user.UserRoles?
                    .SelectMany(ur => ur.Role.RolePermissions ?? new List<RolePermission>())
                    .Select(rp => rp.Permission)
                    .Distinct()
                    .ToList() ?? new List<Permission>();
                userDto.PermissionObjects = _mapper.Map<IEnumerable<PermissionDto>>(permissions);

                userDtos.Add(userDto);
            }

            var pageCount = (int)Math.Ceiling(totalCount / (double)filter.PageSize);

            var result = new PagedResultDto<UserDto>
            {
                Items = userDtos,
                TotalCount = totalCount,
                PageCount = pageCount,
                CurrentPage = filter.PageNumber,
                PageSize = filter.PageSize
            };
            return Result<PagedResultDto<UserDto>>.Success(result);
        }

        public async Task<Result> UpdateUserStatusAsync(Guid userId, bool isActive)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null) return Result.Failure("User not found", ErrorType.NotFound);

            user.IsActive = isActive;
            _unitOfWork.AppUsers.Update(user);
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public async Task<Result<UserDto>> CreateAdminUserAsync(CreateAdminUserDto createAdminUserDto)
        {
            // Validate input
            if (createAdminUserDto.RoleIds == null || !createAdminUserDto.RoleIds.Any())
                return Result<UserDto>.Failure("At least one role must be assigned to admin user", ErrorType.Validation);

            // Check if email already exists
            if (await _unitOfWork.AppUsers.EmailExistsAsync(createAdminUserDto.GetNormalizedEmail()))
                return Result<UserDto>.Failure("At least one role must be assigned to admin user", ErrorType.Conflict);

            // Check if phone already exists
            if (await _unitOfWork.AppUsers.PhoneExistsAsync(createAdminUserDto.Phone))
                return Result<UserDto>.Failure("Phone number already exists", ErrorType.Conflict);

            // Check phone is valid and normalized
            var normalizedPhone = PhoneUtils.NormalizePhoneNumber(createAdminUserDto.Phone);
            if (normalizedPhone == null)
                return Result<UserDto>.Failure("Invalid phone number format", ErrorType.Validation);

            // Validate all roles exist before creating user
            foreach (var roleId in createAdminUserDto.RoleIds)
            {
                var roleExists = await _unitOfWork.AdminRoles.GetByIdAsync(roleId);
                if (roleExists == null)
                    throw new InvalidOperationException($"Role with ID {roleId} not found");
            }

            // Generate salt and hash password
            var salt = GenerateRandomSalt();
            var hashedPassword = HashPassword(createAdminUserDto.Password, salt);

            var user = new AppUser
            {
                Email = createAdminUserDto.GetNormalizedEmail(),
                FullName = createAdminUserDto.FullName,
                PasswordHash = hashedPassword,
                PasswordSalt = salt,
                Phone = normalizedPhone,
                UserType = EnumValues.UserType.Admin,
                CreatedAt = DateTime.UtcNow,
                IsActive = true
            };

            await _unitOfWork.AppUsers.AddAsync(user);

            // Create default notification preferences for the admin user
            var notificationPreferences = new NotificationPreference
            {
                UserId = user.Id,
                ReceivePromotions = true,
                ReceiveWalletUpdates = true,
                ReceiveNews = true,
                ReceiveCustomerMessages = true,
                CreatedAt = DateTime.UtcNow
            };

            await _unitOfWork.NotificationPreferences.AddAsync(notificationPreferences);

            // Add roles to the user (roles already validated above)
            foreach (var roleId in createAdminUserDto.RoleIds)
            {
                var userRole = new UserRole
                {
                    UserID = user.Id,
                    RoleID = roleId
                    // Don't set navigation properties (User, Role) to avoid EF trying to insert them
                };
                await _unitOfWork.UserRoles.AddAsync(userRole);
            }

            // Save all changes in a single transaction - if any operation fails, everything will be rolled back
            await _unitOfWork.SaveChangesAsync();
            return Result<UserDto>.Success(_mapper.Map<UserDto>(user));
        }

        public async Task<Result> UpdateUserRolesAsync(UpdateUserRoleDto updateUserRoleDto)
        {
            // Validate user exists
            var userExists = await _unitOfWork.AppUsers.GetByIdAsync(updateUserRoleDto.UserId);
            if (userExists == null) return Result.Failure("User not found", ErrorType.NotFound);

            // Validate all roles exist before making changes
            foreach (var roleId in updateUserRoleDto.RoleIds)
            {
                var roleExists = await _unitOfWork.AdminRoles.GetByIdAsync(roleId);
                if (roleExists == null)
                    throw new InvalidOperationException($"Role with ID {roleId} not found");
            }

            // Remove existing roles
            var existingRoles = await _unitOfWork.UserRoles.GetQueryable()
                .Where(ur => ur.UserID == updateUserRoleDto.UserId)
                .ToListAsync();

            foreach (var role in existingRoles)
            {
                _unitOfWork.UserRoles.Remove(role);
            }

            // Add new roles
            foreach (var roleId in updateUserRoleDto.RoleIds)
            {
                var userRole = new UserRole
                {
                    UserID = updateUserRoleDto.UserId,
                    RoleID = roleId
                    // Don't set navigation properties to avoid EF trying to insert them
                };
                await _unitOfWork.UserRoles.AddAsync(userRole);
            }

            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public async Task<Result> UpdateUserMainPhoneAsync(UpdateUserPhoneDto updateUserPhoneDto)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(updateUserPhoneDto.UserId);
            if (user == null) return Result.Failure("User not found", ErrorType.NotFound);

            if (string.IsNullOrEmpty(updateUserPhoneDto.Phone))
            {
                return Result.Failure("Invalid phone number format", ErrorType.Validation);
            }

            // Normalize phone number
            var normalizedPhone = PhoneUtils.NormalizePhoneNumber(updateUserPhoneDto.Phone);
            if (normalizedPhone == null)
            {
                return Result.Failure("Invalid phone number format", ErrorType.Validation);
            }

            if (await _unitOfWork.AppUsers.PhoneExistsAsync(normalizedPhone))
            {
                return Result.Failure("Phone number already exists", ErrorType.Conflict);
            }


            user.Phone = normalizedPhone;
            _unitOfWork.AppUsers.Update(user);
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        #endregion

        #region Private Helper Methods

        private static string GenerateRandomSalt()
        {
            byte[] salt = new byte[128 / 8];
            using (var rng = System.Security.Cryptography.RandomNumberGenerator.Create())
            {
                rng.GetBytes(salt);
            }
            return Convert.ToBase64String(salt);
        }

        private static string HashPassword(string password, string salt)
        {
            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                var saltedPassword = string.Concat(password, salt);
                var hashedBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(saltedPassword));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        private IQueryable<AppUser> ApplyUserFilters(IQueryable<AppUser> query, UserFilterDto filter)
        {
            if (!string.IsNullOrEmpty(filter.Email))
                query = query.Where(u => u.Email.Contains(filter.GetNormalizedEmail()));

            if (!string.IsNullOrEmpty(filter.Name))
                query = query.Where(u => u.FullName.ToLower().Contains(filter.Name.ToLower()));

            // Phone filter should be normalized
            if (!string.IsNullOrEmpty(filter.Phone))
            {
                var normalizedPhone = PhoneUtils.NormalizePhoneNumber(filter.Phone);
                if (normalizedPhone != null)
                {
                    query = query.Where(u => u.Phone.Contains(normalizedPhone));
                }
                else
                {
                    // If phone is invalid, return empty result
                    return query.Where(u => false);
                }
            }

            if (filter.UserType != null)
                query = query.Where(u => u.UserType == filter.UserType);

            if (filter.IsActive.HasValue)
                query = query.Where(u => u.IsActive == filter.IsActive.Value);

            return query;
        }

        private IQueryable<AppUser> ApplyUserSorting(IQueryable<AppUser> query, UserFilterDto filter)
        {
            if (string.IsNullOrEmpty(filter.SortColumn))
            {
                return query.OrderByDescending(u => u.CreatedAt);
            }

            switch (filter.SortColumn.ToLower())
            {
                case "fullname":
                case "name":
                    return filter.SortDescending
                        ? query.OrderByDescending(u => u.FullName)
                        : query.OrderBy(u => u.FullName);
                case "email":
                    return filter.SortDescending
                        ? query.OrderByDescending(u => u.Email)
                        : query.OrderBy(u => u.Email);
                case "phone":
                    return filter.SortDescending
                        ? query.OrderByDescending(u => u.Phone)
                        : query.OrderBy(u => u.Phone);
                case "createdat":
                case "created":
                    return filter.SortDescending
                        ? query.OrderByDescending(u => u.CreatedAt)
                        : query.OrderBy(u => u.CreatedAt);
                case "lastlogin":
                    return filter.SortDescending
                        ? query.OrderByDescending(u => u.LastLogin)
                        : query.OrderBy(u => u.LastLogin);
                default:
                    return query.OrderByDescending(u => u.CreatedAt);
            }
        }

        #endregion
    }
}
