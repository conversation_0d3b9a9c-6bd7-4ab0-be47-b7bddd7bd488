﻿namespace Shared.Responses
{
    public class ApiResponse<T>
    {
        /// <summary>
        /// Trạng thái thành công hay thất bại.
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// Dữ liệu trả về nếu thành công (generic).
        /// </summary>
        public T? Data { get; set; }

        /// <summary>
        /// Thông báo chung (ví dụ: "Thao tác thành công").
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// Danh sách các lỗi validation (dùng cho ModelState).
        /// Key là tên trường (field), Value là danh sách thông báo lỗi.
        /// </summary>
        public Dictionary<string, List<string>>? Errors { get; set; }

        // Các hàm khởi tạo tiện ích
        public static ApiResponse<T> Success(T data, string message = "")
        {
            return new ApiResponse<T> { IsSuccess = true, Data = data, Message = message };
        }

        public static ApiResponse<T> Failure(string message, Dictionary<string, List<string>>? errors = null)
        {
            return new ApiResponse<T> { IsSuccess = false, Message = message, Errors = errors };
        }
    }
}
