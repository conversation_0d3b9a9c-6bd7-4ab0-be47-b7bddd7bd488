﻿using RealEstate.Application.DTO;
using Shared.Results;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RealEstate.Application.Interfaces
{
    public interface ISettingService
    {
        Task<Result<List<SettingDto>>> GetAllAsync();
        Task<Result<SettingDto>> GetByKeyAsync(string key);
        Task<Result> CreateAsync(SettingCreateRequest request);
        Task<Result> UpdateAsync(string key, string value);
        Task<Result> BulkUpdateAsync(SettingBulkUpdateRequest request);
    }
}
